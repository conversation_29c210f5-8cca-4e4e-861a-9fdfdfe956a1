#!/bin/bash

# Oracle数据库防火墙配置脚本
# 配置Oracle监听器端口和相关服务端口

echo "========================================="
echo "Oracle Database Firewall Configuration"
echo "========================================="

# 检查当前用户权限
if [ "$EUID" -ne 0 ]; then
  echo "❌ This script must be run as root or with sudo"
  echo "Usage: sudo $0"
  exit 1
fi

# 检查firewall-cmd是否可用
if ! command -v firewall-cmd &> /dev/null; then
  echo "❌ firewall-cmd not found. Please install firewalld first."
  exit 1
fi

# 检查防火墙状态
echo "Checking firewall status..."
systemctl status firewalld --no-pager -l

if ! systemctl is-active --quiet firewalld; then
  echo "⚠️  Firewalld is not running. Starting firewalld..."
  systemctl start firewalld
  systemctl enable firewalld
fi

echo ""
echo "=== Current Firewall Configuration ==="
firewall-cmd --list-all

echo ""
echo "=== Configuring Oracle Database Ports ==="

# Oracle监听器端口 (默认1521)
echo "Opening Oracle Listener port 1521/tcp..."
firewall-cmd --permanent --add-port=1521/tcp
if [ $? -eq 0 ]; then
  echo "✅ Port 1521/tcp added successfully"
else
  echo "❌ Failed to add port 1521/tcp"
fi

# Oracle Enterprise Manager端口 (可选)
echo "Opening Oracle EM Express port 5500/tcp..."
firewall-cmd --permanent --add-port=5500/tcp
if [ $? -eq 0 ]; then
  echo "✅ Port 5500/tcp added successfully"
else
  echo "❌ Failed to add port 5500/tcp"
fi

# Oracle XMLDB HTTP端口 (可选)
echo "Opening Oracle XMLDB HTTP port 8080/tcp..."
firewall-cmd --permanent --add-port=8080/tcp
if [ $? -eq 0 ]; then
  echo "✅ Port 8080/tcp added successfully"
else
  echo "❌ Failed to add port 8080/tcp"
fi

# Oracle XMLDB HTTPS端口 (可选)
echo "Opening Oracle XMLDB HTTPS port 8443/tcp..."
firewall-cmd --permanent --add-port=8443/tcp
if [ $? -eq 0 ]; then
  echo "✅ Port 8443/tcp added successfully"
else
  echo "❌ Failed to add port 8443/tcp"
fi

# 重新加载防火墙配置
echo ""
echo "Reloading firewall configuration..."
firewall-cmd --reload
if [ $? -eq 0 ]; then
  echo "✅ Firewall configuration reloaded successfully"
else
  echo "❌ Failed to reload firewall configuration"
  exit 1
fi

echo ""
echo "=== Updated Firewall Configuration ==="
firewall-cmd --list-all

echo ""
echo "=== Verifying Oracle Ports ==="
echo "Checking if Oracle ports are open:"
firewall-cmd --query-port=1521/tcp && echo "✅ Port 1521/tcp is open" || echo "❌ Port 1521/tcp is not open"
firewall-cmd --query-port=5500/tcp && echo "✅ Port 5500/tcp is open" || echo "❌ Port 5500/tcp is not open"
firewall-cmd --query-port=8080/tcp && echo "✅ Port 8080/tcp is open" || echo "❌ Port 8080/tcp is not open"
firewall-cmd --query-port=8443/tcp && echo "✅ Port 8443/tcp is open" || echo "❌ Port 8443/tcp is not open"

echo ""
echo "=== Testing Oracle Listener ==="
if command -v lsnrctl &> /dev/null; then
  echo "Checking Oracle Listener status..."
  su - oracle -c "lsnrctl status" 2>/dev/null || echo "⚠️  Oracle Listener not running or not accessible"
else
  echo "⚠️  lsnrctl command not found"
fi

echo ""
echo "=== Network Connectivity Test ==="
echo "Testing local connectivity to Oracle port 1521..."
if command -v nc &> /dev/null; then
  timeout 3 nc -z localhost 1521 && echo "✅ Port 1521 is accessible locally" || echo "⚠️  Port 1521 is not accessible locally (Oracle may not be running)"
elif command -v telnet &> /dev/null; then
  timeout 3 telnet localhost 1521 </dev/null && echo "✅ Port 1521 is accessible locally" || echo "⚠️  Port 1521 is not accessible locally (Oracle may not be running)"
else
  echo "⚠️  nc or telnet not available for connectivity test"
fi

echo ""
echo "========================================="
echo "Oracle Firewall Configuration Complete!"
echo "========================================="
echo ""
echo "📋 Summary of configured ports:"
echo "  • 1521/tcp - Oracle Listener (required)"
echo "  • 5500/tcp - Oracle EM Express (optional)"
echo "  • 8080/tcp - Oracle XMLDB HTTP (optional)"
echo "  • 8443/tcp - Oracle XMLDB HTTPS (optional)"
echo ""
echo "🔧 Next steps:"
echo "  1. Ensure Oracle Database is running"
echo "  2. Start Oracle Listener: su - oracle -c 'lsnrctl start'"
echo "  3. Test remote connectivity from client applications"
echo ""
echo "🔍 Troubleshooting:"
echo "  • Check Oracle status: su - oracle -c 'sqlplus / as sysdba'"
echo "  • Check listener status: su - oracle -c 'lsnrctl status'"
echo "  • View firewall logs: journalctl -u firewalld -f"
