#!/bin/bash

# Oracle环境检查脚本
echo "========================================="
echo "Oracle Environment Check Script"
echo "========================================="

# 检查当前用户
echo "Current User: $(whoami)"
echo "Current Directory: $(pwd)"

# 检查环境变量
echo ""
echo "=== Environment Variables ==="
echo "ORACLE_HOME: ${ORACLE_HOME:-'NOT SET'}"
echo "ORACLE_SID: ${ORACLE_SID:-'NOT SET'}"
echo "PATH: $PATH"
echo "LD_LIBRARY_PATH: ${LD_LIBRARY_PATH:-'NOT SET'}"

# 检查Oracle主目录
echo ""
echo "=== Oracle Home Directory Check ==="
if [ -n "$ORACLE_HOME" ]; then
  if [ -d "$ORACLE_HOME" ]; then
    echo "✅ ORACLE_HOME directory exists: $ORACLE_HOME"
    ls -la "$ORACLE_HOME" | head -10
  else
    echo "❌ ORACLE_HOME directory does not exist: $ORACLE_HOME"
  fi
else
  echo "❌ ORACLE_HOME is not set"
  echo "Common Oracle Home locations:"
  find /opt -name "oracle" -type d 2>/dev/null | head -5
  find /u01 -name "oracle" -type d 2>/dev/null | head -5
fi

# 检查Oracle二进制文件
echo ""
echo "=== Oracle Binary Files Check ==="
if [ -n "$ORACLE_HOME" ] && [ -d "$ORACLE_HOME/bin" ]; then
  echo "Oracle bin directory: $ORACLE_HOME/bin"
  if [ -f "$ORACLE_HOME/bin/sqlplus" ]; then
    echo "✅ sqlplus found"
    "$ORACLE_HOME/bin/sqlplus" -v 2>/dev/null || echo "sqlplus version check failed"
  else
    echo "❌ sqlplus not found"
  fi
  
  if [ -f "$ORACLE_HOME/bin/rman" ]; then
    echo "✅ rman found"
  else
    echo "❌ rman not found"
  fi
else
  echo "❌ Oracle bin directory not accessible"
fi

# 检查Oracle进程
echo ""
echo "=== Oracle Processes ==="
ps -ef | grep -v grep | grep oracle | head -10

# 检查Oracle监听器
echo ""
echo "=== Oracle Listener Status ==="
if command -v lsnrctl >/dev/null 2>&1; then
  lsnrctl status 2>/dev/null || echo "Listener not running or not accessible"
else
  echo "lsnrctl command not found"
fi

# 检查Oracle实例状态
echo ""
echo "=== Oracle Instance Status ==="
if [ -n "$ORACLE_HOME" ] && [ -f "$ORACLE_HOME/bin/sqlplus" ]; then
  echo "Attempting to connect to Oracle..."
  "$ORACLE_HOME/bin/sqlplus" -s / as sysdba <<EOF 2>/dev/null
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT 'Instance Status: ' || STATUS FROM V\$INSTANCE;
SELECT 'Database Status: ' || OPEN_MODE FROM V\$DATABASE;
EXIT;
EOF
  if [ $? -ne 0 ]; then
    echo "❌ Cannot connect to Oracle instance"
  fi
else
  echo "❌ Cannot check Oracle instance - sqlplus not available"
fi

# 检查备份目录
echo ""
echo "=== Backup Directory Check ==="
BACKUP_DIR="/opt/oracle/rman_backup"
if [ -d "$BACKUP_DIR" ]; then
  echo "✅ Backup directory exists: $BACKUP_DIR"
  echo "Directory contents:"
  ls -la "$BACKUP_DIR" | head -10
  echo "Disk space:"
  df -h "$BACKUP_DIR"
else
  echo "❌ Backup directory does not exist: $BACKUP_DIR"
fi

# 检查Oracle配置文件
echo ""
echo "=== Oracle Configuration Files ==="
if [ -n "$ORACLE_HOME" ]; then
  DBS_DIR="$ORACLE_HOME/dbs"
  if [ -d "$DBS_DIR" ]; then
    echo "DBS directory: $DBS_DIR"
    ls -la "$DBS_DIR" | grep -E "(spfile|init|orapw)" || echo "No configuration files found"
  fi
fi

echo ""
echo "========================================="
echo "Environment check completed"
echo "========================================="
