#!/bin/bash

# 优化当前自定义归档目录配置
# 功能：保持当前配置，但增加监控和管理功能

echo "========================================="
echo "优化当前归档配置"
echo "开始时间: $(date)"
echo "========================================="

# 设置变量
ARCHIVE_LOG_DIR="/opt/oracle/archivelog"
FRA_DIR="/opt/oracle/flash_recovery_area"
BACKUP_DIR="/opt/oracle/rman_backup"

# 1. 处理FRA中的旧归档文件
echo ""
echo "=== 处理FRA中的旧归档文件 ==="
if [ -d "$FRA_DIR" ]; then
    OLD_ARCHIVE_COUNT=$(find "$FRA_DIR" -name "*.arc" -type f 2>/dev/null | wc -l)
    echo "FRA中发现 $OLD_ARCHIVE_COUNT 个旧归档文件"
    
    if [ "$OLD_ARCHIVE_COUNT" -gt 0 ]; then
        echo "选择处理方式："
        echo "1) 移动到新的归档目录"
        echo "2) 备份后删除"
        echo "3) 保留不动"
        read -p "请选择 (1-3): " choice
        
        case $choice in
            1)
                echo "移动旧归档文件到新目录..."
                mkdir -p "$ARCHIVE_LOG_DIR/migrated_from_fra"
                find "$FRA_DIR" -name "*.arc" -type f -exec mv {} "$ARCHIVE_LOG_DIR/migrated_from_fra/" \;
                echo "已移动 $OLD_ARCHIVE_COUNT 个文件到 $ARCHIVE_LOG_DIR/migrated_from_fra/"
                ;;
            2)
                echo "备份旧归档文件..."
                mkdir -p "$BACKUP_DIR/old_fra_archives"
                find "$FRA_DIR" -name "*.arc" -type f -exec cp {} "$BACKUP_DIR/old_fra_archives/" \;
                find "$FRA_DIR" -name "*.arc" -type f -delete
                echo "已备份并删除旧归档文件"
                ;;
            3)
                echo "保留FRA中的旧归档文件不动"
                ;;
        esac
    fi
else
    echo "FRA目录不存在，无需处理"
fi

# 2. 优化当前归档目录
echo ""
echo "=== 优化当前归档目录 ==="
if [ ! -d "$ARCHIVE_LOG_DIR" ]; then
    mkdir -p "$ARCHIVE_LOG_DIR"
    chown oracle:oinstall "$ARCHIVE_LOG_DIR"
    chmod 755 "$ARCHIVE_LOG_DIR"
    echo "创建归档目录: $ARCHIVE_LOG_DIR"
fi

# 检查权限
if [ -w "$ARCHIVE_LOG_DIR" ]; then
    echo "✓ 归档目录权限正常"
else
    echo "✗ 归档目录权限异常，正在修复..."
    chown oracle:oinstall "$ARCHIVE_LOG_DIR"
    chmod 755 "$ARCHIVE_LOG_DIR"
fi

# 3. 创建归档日志管理脚本
echo ""
echo "=== 创建归档日志管理脚本 ==="
cat > "$ARCHIVE_LOG_DIR/manage_archive_logs.sh" <<'EOF'
#!/bin/bash

# 归档日志管理脚本
ARCHIVE_DIR="/opt/oracle/archivelog"
RETENTION_DAYS=7

echo "========================================="
echo "归档日志管理 - $(date)"
echo "========================================="

# 显示当前使用情况
echo "当前归档目录使用情况:"
du -sh "$ARCHIVE_DIR"
df -h "$ARCHIVE_DIR"

# 显示文件数量
TOTAL_FILES=$(find "$ARCHIVE_DIR" -name "*.arc" -type f | wc -l)
echo "总归档文件数量: $TOTAL_FILES"

# 显示最新和最旧的文件
echo ""
echo "最新的5个归档文件:"
find "$ARCHIVE_DIR" -name "*.arc" -type f -exec ls -lt {} \; | head -5

echo ""
echo "最旧的5个归档文件:"
find "$ARCHIVE_DIR" -name "*.arc" -type f -exec ls -lt {} \; | tail -5

# 清理旧文件（可选）
OLD_FILES=$(find "$ARCHIVE_DIR" -name "*.arc" -type f -mtime +$RETENTION_DAYS | wc -l)
if [ "$OLD_FILES" -gt 0 ]; then
    echo ""
    echo "发现 $OLD_FILES 个超过 $RETENTION_DAYS 天的归档文件"
    read -p "是否删除这些旧文件? (y/N): " confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        find "$ARCHIVE_DIR" -name "*.arc" -type f -mtime +$RETENTION_DAYS -delete
        echo "已删除旧的归档文件"
    fi
fi

echo "========================================="
EOF

chmod +x "$ARCHIVE_LOG_DIR/manage_archive_logs.sh"
echo "创建归档管理脚本: $ARCHIVE_LOG_DIR/manage_archive_logs.sh"

# 4. 创建监控脚本
echo ""
echo "=== 创建监控脚本 ==="
cat > "$BACKUP_DIR/monitor_archive_space.sh" <<'EOF'
#!/bin/bash

# 归档空间监控脚本
ARCHIVE_DIR="/opt/oracle/archivelog"
THRESHOLD=80  # 使用率阈值

# 检查磁盘使用率
USAGE=$(df "$ARCHIVE_DIR" | awk 'NR==2 {print $5}' | tr -d '%')

echo "归档目录磁盘使用率: ${USAGE}%"

if [ "$USAGE" -gt "$THRESHOLD" ]; then
    echo "警告: 归档目录磁盘使用率超过阈值 (${USAGE}% > ${THRESHOLD}%)"
    echo "建议执行归档日志清理"
    
    # 可以在这里添加告警通知
    # echo "归档空间告警: ${USAGE}%" | mail -s "Oracle Archive Space Alert" <EMAIL>
fi

# 检查归档文件数量
ARCHIVE_COUNT=$(find "$ARCHIVE_DIR" -name "*.arc" -type f | wc -l)
echo "当前归档文件数量: $ARCHIVE_COUNT"

if [ "$ARCHIVE_COUNT" -gt 1000 ]; then
    echo "警告: 归档文件数量过多 ($ARCHIVE_COUNT > 1000)"
fi
EOF

chmod +x "$BACKUP_DIR/monitor_archive_space.sh"
echo "创建监控脚本: $BACKUP_DIR/monitor_archive_space.sh"

# 5. 添加到crontab建议
echo ""
echo "=== Crontab配置建议 ==="
echo "建议添加以下crontab任务进行定期监控:"
echo ""
echo "# 每小时检查归档空间"
echo "0 * * * * $BACKUP_DIR/monitor_archive_space.sh >> $BACKUP_DIR/logs/archive_monitor.log 2>&1"
echo ""
echo "# 每天凌晨清理旧归档（可选）"
echo "0 3 * * * find $ARCHIVE_LOG_DIR -name '*.arc' -mtime +7 -delete"

# 6. 验证当前配置
echo ""
echo "=== 验证当前配置 ==="
sqlplus -s / as sysdba <<EOF
SELECT 'Archive Destination: ' || DESTINATION FROM V\$ARCHIVE_DEST WHERE DEST_ID=1;
EOF

# 测试日志切换
echo "测试日志切换..."
sqlplus -s / as sysdba <<EOF
ALTER SYSTEM SWITCH LOGFILE;
EOF

sleep 2

# 检查新归档文件
NEW_ARCHIVE=$(find "$ARCHIVE_LOG_DIR" -name "*.arc" -type f -mmin -2 | head -1)
if [ -n "$NEW_ARCHIVE" ]; then
    echo "✓ 归档配置正常，新归档文件: $(basename $NEW_ARCHIVE)"
else
    echo "✗ 可能存在问题，未发现新的归档文件"
fi

echo ""
echo "========================================="
echo "当前配置优化完成"
echo "归档目录: $ARCHIVE_LOG_DIR"
echo "管理脚本: $ARCHIVE_LOG_DIR/manage_archive_logs.sh"
echo "监控脚本: $BACKUP_DIR/monitor_archive_space.sh"
echo "========================================="
