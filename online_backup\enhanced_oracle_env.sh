#!/bin/bash

# Oracle 增强环境配置脚本 - 支持FRA和自定义归档目录
# 功能：提供灵活的归档配置选择，支持FRA或自定义目录

# =============================================================================
# 配置选项
# =============================================================================

# 归档配置模式选择
# CUSTOM_ONLY: 仅使用自定义目录
# FRA_ONLY: 仅使用Flash Recovery Area
# HYBRID: 混合模式（推荐）
ARCHIVE_MODE="HYBRID"

# 基础环境变量
export ORACLE_SID=PDBQZ
export ORACLE_HOME=/opt/oracle/product/11gR2/db
export PATH=$ORACLE_HOME/bin:$PATH
export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH

# 存储路径配置
export BACKUP_BASE_DIR="/opt/oracle/rman_backup"
export ARCHIVE_LOG_DIR="/opt/oracle/archivelog"
export FRA_DIR="/opt/oracle/fra"
export BACKUP_LOG_DIR="$BACKUP_BASE_DIR/logs"

# FRA配置参数
FRA_SIZE="50G"  # FRA大小
FRA_USAGE_THRESHOLD=80  # FRA使用率告警阈值

# =============================================================================
# 日志函数
# =============================================================================

log_info() {
    echo -e "\033[0;34m[INFO]\033[0m $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "\033[0;32m[SUCCESS]\033[0m $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "\033[0;31m[ERROR]\033[0m $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# =============================================================================
# 目录管理函数
# =============================================================================

# 创建必要目录
create_directories() {
    log_info "创建必要的目录结构..."
    
    local dirs=("$BACKUP_BASE_DIR" "$BACKUP_LOG_DIR")
    
    # 根据归档模式添加相应目录
    case "$ARCHIVE_MODE" in
        "CUSTOM_ONLY"|"HYBRID")
            dirs+=("$ARCHIVE_LOG_DIR")
            ;;
        "FRA_ONLY"|"HYBRID")
            dirs+=("$FRA_DIR")
            ;;
    esac
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            if [ $? -eq 0 ]; then
                log_success "目录创建成功: $dir"
                chown oracle:oinstall "$dir" 2>/dev/null || true
                chmod 755 "$dir"
            else
                log_error "目录创建失败: $dir"
                return 1
            fi
        else
            log_info "目录已存在: $dir"
        fi
    done
    
    return 0
}

# =============================================================================
# 归档配置检查函数
# =============================================================================

# 检查FRA配置
check_fra_config() {
    log_info "检查Flash Recovery Area配置..."
    
    local fra_dest=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT VALUE FROM V\$PARAMETER WHERE NAME = 'db_recovery_file_dest';
EXIT;
EOF
)
    
    local fra_size=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT VALUE FROM V\$PARAMETER WHERE NAME = 'db_recovery_file_dest_size';
EXIT;
EOF
)
    
    if [ -n "$fra_dest" ] && [ "$fra_dest" != "" ]; then
        log_success "FRA目录: $fra_dest"
        log_success "FRA大小: $fra_size"
        
        # 检查FRA使用情况
        local fra_usage=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT ROUND((SPACE_USED/SPACE_LIMIT)*100,2) FROM V\$RECOVERY_FILE_DEST;
EXIT;
EOF
)
        
        if [ -n "$fra_usage" ] && [ "$fra_usage" != "" ]; then
            log_info "FRA使用率: ${fra_usage}%"
            
            if (( $(echo "$fra_usage > $FRA_USAGE_THRESHOLD" | bc -l) )); then
                log_warning "FRA使用率过高: ${fra_usage}% (阈值: ${FRA_USAGE_THRESHOLD}%)"
            fi
        fi
    else
        log_warning "FRA未配置"
    fi
}

# 检查归档目标配置
check_archive_destinations() {
    log_info "检查归档日志目标配置..."
    
    sqlplus -s / as sysdba <<EOF
SET PAGESIZE 1000 LINESIZE 200
SELECT 
    DEST_ID,
    DESTINATION,
    STATUS,
    BINDING,
    VALID_FOR,
    ERROR
FROM V\$ARCHIVE_DEST 
WHERE DEST_ID <= 5 AND STATUS != 'INACTIVE'
ORDER BY DEST_ID;
EXIT;
EOF
    
    # 检查归档日志生成情况
    local archive_count=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT COUNT(*) FROM V\$ARCHIVED_LOG WHERE FIRST_TIME > SYSDATE - 1;
EXIT;
EOF
)
    
    log_info "最近24小时生成的归档日志数量: $archive_count"
}

# =============================================================================
# 配置建议函数
# =============================================================================

# 提供配置建议
provide_configuration_advice() {
    log_info "========================================="
    log_info "归档配置建议分析"
    log_info "========================================="
    
    case "$ARCHIVE_MODE" in
        "CUSTOM_ONLY")
            log_info "当前模式: 仅自定义目录"
            log_info "优势: 路径透明、便于管理、性能可控"
            log_info "劣势: 需要手动空间管理、缺少自动清理"
            log_warning "建议: 实现自动清理脚本，监控磁盘空间"
            ;;
        "FRA_ONLY")
            log_info "当前模式: 仅Flash Recovery Area"
            log_info "优势: 自动空间管理、统一存储、智能清理"
            log_info "劣势: 路径不透明、配置相对复杂"
            log_warning "建议: 定期监控FRA使用率，适当调整大小"
            ;;
        "HYBRID")
            log_info "当前模式: 混合模式（推荐）"
            log_info "优势: 结合两种方式的优点、双重保护"
            log_info "注意: 需要管理两套存储、监控两个位置"
            log_success "建议: 这是推荐的企业级配置"
            ;;
    esac
    
    # 存储空间建议
    log_info "存储空间建议:"
    log_info "- 归档日志目录: 至少保留3-7天的归档日志空间"
    log_info "- FRA: 建议设置为数据库大小的2-3倍"
    log_info "- 备份目录: 建议设置为数据库大小的1.5-2倍"
    
    # 性能建议
    log_info "性能优化建议:"
    log_info "- 将归档日志放在独立的高速存储上"
    log_info "- 避免归档日志与数据文件共享存储"
    log_info "- 定期监控归档日志生成速度"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "========================================="
    log_info "Oracle增强环境配置检查"
    log_info "归档模式: $ARCHIVE_MODE"
    log_info "开始时间: $(date)"
    log_info "========================================="
    
    # 创建目录
    if ! create_directories; then
        log_error "目录创建失败"
        exit 1
    fi
    
    # 检查Oracle基础环境
    log_info "检查Oracle基础环境..."
    if ! sqlplus -s / as sysdba <<< "SELECT 1 FROM DUAL;" > /dev/null 2>&1; then
        log_error "无法连接到Oracle数据库"
        exit 1
    fi
    log_success "Oracle数据库连接正常"
    
    # 检查数据库状态
    local db_status=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT STATUS FROM V\$INSTANCE;
EXIT;
EOF
)
    log_info "数据库状态: $db_status"
    
    # 检查归档模式
    local archive_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT LOG_MODE FROM V\$DATABASE;
EXIT;
EOF
)
    log_info "数据库归档模式: $archive_mode"
    
    # 根据配置模式进行相应检查
    case "$ARCHIVE_MODE" in
        "FRA_ONLY"|"HYBRID")
            check_fra_config
            ;;
    esac
    
    # 检查归档目标
    check_archive_destinations
    
    # 提供配置建议
    provide_configuration_advice
    
    log_info "========================================="
    log_success "环境配置检查完成"
    log_info "========================================="
}

# 如果直接运行此脚本，则执行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
