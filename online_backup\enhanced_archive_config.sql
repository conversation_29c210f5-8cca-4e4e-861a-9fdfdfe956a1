-- =====================================================================
-- Oracle归档日志配置优化脚本 - 混合FRA方案
-- 功能：结合FRA和自定义目录的优势，提供更灵活的归档配置
-- 适用：Oracle 11gR2及以上版本
-- =====================================================================

-- 设置SQL*Plus环境
SET ECHO ON
SET FEEDBACK ON
SET PAGESIZE 1000
SET LINESIZE 200
SET TIMING ON

PROMPT =====================================================================
PROMPT 配置Flash Recovery Area (FRA) + 自定义归档目录的混合方案
PROMPT =====================================================================

-- 1. 配置Flash Recovery Area
PROMPT 步骤1: 配置Flash Recovery Area
ALTER SYSTEM SET db_recovery_file_dest_size=50G SCOPE=SPFILE;
ALTER SYSTEM SET db_recovery_file_dest='/opt/oracle/fra' SCOPE=SPFILE;

-- 2. 配置多个归档目标
PROMPT 步骤2: 配置多个归档日志目标

-- 主归档目标：使用FRA（自动管理）
ALTER SYSTEM SET log_archive_dest_1='LOCATION=USE_DB_RECOVERY_FILE_DEST VALID_FOR=(ALL_LOGFILES,ALL_ROLES) DB_UNIQUE_NAME=PDBQZ' SCOPE=SPFILE;
ALTER SYSTEM SET log_archive_dest_state_1='ENABLE' SCOPE=SPFILE;

-- 辅助归档目标：使用自定义目录（手动管理，用于快速访问）
ALTER SYSTEM SET log_archive_dest_2='LOCATION=/opt/oracle/archivelog VALID_FOR=(ALL_LOGFILES,ALL_ROLES) DB_UNIQUE_NAME=PDBQZ' SCOPE=SPFILE;
ALTER SYSTEM SET log_archive_dest_state_2='ENABLE' SCOPE=SPFILE;

-- 3. 配置归档格式
PROMPT 步骤3: 配置归档日志格式
ALTER SYSTEM SET log_archive_format='arch_%t_%s_%r.arc' SCOPE=SPFILE;

-- 4. 配置最小成功归档目标数
PROMPT 步骤4: 配置归档策略
ALTER SYSTEM SET log_archive_min_succeed_dest=1 SCOPE=SPFILE;

-- 5. 配置RMAN保留策略
PROMPT 步骤5: 配置RMAN保留策略
RMAN TARGET / <<EOF
CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 7 DAYS;
CONFIGURE ARCHIVELOG DELETION POLICY TO APPLIED ON ALL STANDBY BACKED UP 1 TIMES TO DISK;
CONFIGURE BACKUP OPTIMIZATION ON;
EXIT;
EOF

-- 6. 显示配置结果
PROMPT =====================================================================
PROMPT 配置结果验证
PROMPT =====================================================================

SELECT 
    'FRA Location: ' || VALUE AS FRA_CONFIG
FROM V$PARAMETER 
WHERE NAME = 'db_recovery_file_dest';

SELECT 
    'FRA Size: ' || VALUE AS FRA_SIZE
FROM V$PARAMETER 
WHERE NAME = 'db_recovery_file_dest_size';

SELECT 
    DEST_ID,
    DESTINATION,
    STATUS,
    BINDING,
    VALID_FOR
FROM V$ARCHIVE_DEST 
WHERE DEST_ID <= 5 AND STATUS != 'INACTIVE';

PROMPT =====================================================================
PROMPT 混合归档配置的优势：
PROMPT 1. FRA提供自动空间管理和统一存储
PROMPT 2. 自定义目录提供快速访问和灵活控制
PROMPT 3. 双重保护确保归档日志安全性
PROMPT 4. 可以根据需要选择不同的归档源进行恢复
PROMPT =====================================================================

EXIT;
