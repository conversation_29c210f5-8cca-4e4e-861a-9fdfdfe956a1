#!/bin/bash

# Oracle 备份策略管理脚本 - 企业级统一管理工具
# 功能：统一管理在线备份和离线备份策略，提供灵活的配置和调度功能

# =============================================================================
# 脚本配置和环境加载
# =============================================================================

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PARENT_DIR="$(dirname "$SCRIPT_DIR")"

# 加载在线备份环境配置
source "$SCRIPT_DIR/0_oracle_env.sh"

# 策略管理配置
STRATEGY_CONFIG_FILE="$BACKUP_BASE_DIR/config/backup_strategy.conf"
STRATEGY_LOG_FILE="$BACKUP_LOG_DIR/strategy_manager_$(date +%Y%m%d_%H%M%S).log"
LOCK_FILE="/tmp/backup_strategy_manager.lock"

# 创建配置目录
mkdir -p "$BACKUP_BASE_DIR/config"
mkdir -p "$BACKUP_LOG_DIR"

# =============================================================================
# 日志和工具函数
# =============================================================================

# 日志函数
log_strategy() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$STRATEGY_LOG_FILE"
}

log_strategy_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$STRATEGY_LOG_FILE" >&2
}

log_strategy_success() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1" | tee -a "$STRATEGY_LOG_FILE"
}

log_strategy_warning() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1" | tee -a "$STRATEGY_LOG_FILE"
}

# 清理函数
cleanup() {
    rm -f "$LOCK_FILE"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# =============================================================================
# 配置管理函数
# =============================================================================

# 创建默认配置文件
create_default_config() {
    log_strategy "创建默认备份策略配置..."
    
    cat > "$STRATEGY_CONFIG_FILE" <<EOF
# Oracle 备份策略配置文件
# 生成时间: $(date)

[GENERAL]
# 备份策略类型: ONLINE_ONLY, OFFLINE_ONLY, HYBRID
BACKUP_STRATEGY=HYBRID

# 业务等级: CRITICAL, IMPORTANT, NORMAL
BUSINESS_TIER=IMPORTANT

# RTO目标 (分钟)
RTO_TARGET=240

# RPO目标 (分钟)
RPO_TARGET=60

[ONLINE_BACKUP]
# 启用在线备份
ENABLE_ONLINE_BACKUP=true

# Level 0备份频率 (天)
LEVEL0_FREQUENCY=7

# Level 1备份频率 (天)
LEVEL1_FREQUENCY=1

# 备份开始时间 (HH:MM)
BACKUP_START_TIME=02:00

# 最大备份并发通道数
MAX_CHANNELS=4

# 压缩算法: BASIC, MEDIUM, HIGH
COMPRESSION_ALGORITHM=MEDIUM

[OFFLINE_BACKUP]
# 启用离线备份
ENABLE_OFFLINE_BACKUP=true

# 离线备份频率 (天)
OFFLINE_FREQUENCY=30

# 离线备份时间 (HH:MM)
OFFLINE_START_TIME=03:00

[RETENTION]
# 在线备份保留天数
ONLINE_RETENTION_DAYS=30

# 离线备份保留天数
OFFLINE_RETENTION_DAYS=365

# 归档日志保留天数
ARCHIVELOG_RETENTION_DAYS=7

[MONITORING]
# 启用邮件通知
ENABLE_EMAIL_NOTIFICATION=false

# 邮件地址
EMAIL_RECIPIENTS=""

# 启用短信通知
ENABLE_SMS_NOTIFICATION=false

# 短信号码
SMS_RECIPIENTS=""

[STORAGE]
# 备份存储路径
BACKUP_STORAGE_PATH=$BACKUP_BASE_DIR

# 最大存储使用率 (%)
MAX_STORAGE_USAGE=80

# 自动清理启用
AUTO_CLEANUP_ENABLED=true
EOF
    
    log_strategy_success "默认配置文件已创建: $STRATEGY_CONFIG_FILE"
}

# 读取配置
read_config() {
    if [ ! -f "$STRATEGY_CONFIG_FILE" ]; then
        log_strategy_warning "配置文件不存在，创建默认配置"
        create_default_config
    fi
    
    # 读取配置变量
    source "$STRATEGY_CONFIG_FILE"
    log_strategy "配置文件已加载: $STRATEGY_CONFIG_FILE"
}

# 验证配置
validate_config() {
    log_strategy "验证备份策略配置..."
    
    local config_valid=true
    
    # 检查必要的配置项
    if [ -z "$BACKUP_STRATEGY" ]; then
        log_strategy_error "BACKUP_STRATEGY未配置"
        config_valid=false
    fi
    
    if [ -z "$BUSINESS_TIER" ]; then
        log_strategy_error "BUSINESS_TIER未配置"
        config_valid=false
    fi
    
    if [ -z "$RTO_TARGET" ] || [ "$RTO_TARGET" -le 0 ]; then
        log_strategy_error "RTO_TARGET配置无效"
        config_valid=false
    fi
    
    if [ -z "$RPO_TARGET" ] || [ "$RPO_TARGET" -le 0 ]; then
        log_strategy_error "RPO_TARGET配置无效"
        config_valid=false
    fi
    
    if [ "$config_valid" = true ]; then
        log_strategy_success "配置验证通过"
        return 0
    else
        log_strategy_error "配置验证失败"
        return 1
    fi
}

# =============================================================================
# 备份策略执行函数
# =============================================================================

# 执行在线备份策略
execute_online_backup_strategy() {
    log_strategy "========================================="
    log_strategy "执行在线备份策略"
    log_strategy "========================================="
    
    if [ "$ENABLE_ONLINE_BACKUP" != "true" ]; then
        log_strategy "在线备份未启用，跳过"
        return 0
    fi
    
    local current_day=$(date +%u)  # 1=Monday, 7=Sunday
    local current_time=$(date +%H%M)
    local backup_time=$(echo "$BACKUP_START_TIME" | tr -d ':')
    
    # 检查是否到了备份时间
    if [ "$current_time" -lt "$backup_time" ]; then
        log_strategy "尚未到达备份时间 ($BACKUP_START_TIME)，当前时间: $(date +%H:%M)"
        return 0
    fi
    
    # 确定备份类型
    local backup_type=""
    if [ $((current_day % LEVEL0_FREQUENCY)) -eq 0 ]; then
        backup_type="LEVEL0"
    elif [ $((current_day % LEVEL1_FREQUENCY)) -eq 0 ]; then
        backup_type="LEVEL1"
    else
        log_strategy "今日无需执行在线备份"
        return 0
    fi
    
    log_strategy "执行 $backup_type 在线备份..."
    
    # 执行相应的备份脚本
    case "$backup_type" in
        "LEVEL0")
            log_strategy "启动Level 0全量备份..."
            "$SCRIPT_DIR/2_rman_backup_level0.sh"
            local exit_code=$?
            ;;
        "LEVEL1")
            log_strategy "启动Level 1增量备份..."
            "$SCRIPT_DIR/3_rman_backup_level1.sh"
            local exit_code=$?
            ;;
    esac
    
    if [ $exit_code -eq 0 ]; then
        log_strategy_success "$backup_type 在线备份执行成功"
        send_notification "SUCCESS" "$backup_type 在线备份成功完成"
    else
        log_strategy_error "$backup_type 在线备份执行失败"
        send_notification "FAILED" "$backup_type 在线备份失败"
        return 1
    fi
    
    return 0
}

# 执行离线备份策略
execute_offline_backup_strategy() {
    log_strategy "========================================="
    log_strategy "执行离线备份策略"
    log_strategy "========================================="
    
    if [ "$ENABLE_OFFLINE_BACKUP" != "true" ]; then
        log_strategy "离线备份未启用，跳过"
        return 0
    fi
    
    local current_day=$(date +%d)
    local current_time=$(date +%H%M)
    local backup_time=$(echo "$OFFLINE_START_TIME" | tr -d ':')
    
    # 检查是否到了离线备份时间（每月第一天）
    if [ "$current_day" -ne 1 ] || [ "$current_time" -lt "$backup_time" ]; then
        log_strategy "非离线备份执行时间，跳过"
        return 0
    fi
    
    log_strategy "执行离线备份..."
    
    # 检查离线备份脚本是否存在
    local offline_script="$PARENT_DIR/offline_backup/rman_cold_backup.sh"
    if [ ! -f "$offline_script" ]; then
        log_strategy_error "离线备份脚本不存在: $offline_script"
        return 1
    fi
    
    # 执行离线备份
    "$offline_script"
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_strategy_success "离线备份执行成功"
        send_notification "SUCCESS" "离线备份成功完成"
    else
        log_strategy_error "离线备份执行失败"
        send_notification "FAILED" "离线备份失败"
        return 1
    fi
    
    return 0
}

# 执行混合备份策略
execute_hybrid_backup_strategy() {
    log_strategy "========================================="
    log_strategy "执行混合备份策略"
    log_strategy "========================================="
    
    # 优先执行在线备份
    execute_online_backup_strategy
    local online_result=$?
    
    # 在适当时机执行离线备份
    execute_offline_backup_strategy
    local offline_result=$?
    
    if [ $online_result -eq 0 ] && [ $offline_result -eq 0 ]; then
        log_strategy_success "混合备份策略执行成功"
        return 0
    else
        log_strategy_error "混合备份策略执行部分失败"
        return 1
    fi
}

# =============================================================================
# 监控和通知函数
# =============================================================================

# 发送通知
send_notification() {
    local status=$1
    local message=$2
    
    log_strategy "发送通知: $status - $message"
    
    # 邮件通知
    if [ "$ENABLE_EMAIL_NOTIFICATION" = "true" ] && [ -n "$EMAIL_RECIPIENTS" ]; then
        echo "$message" | mail -s "Oracle Backup $status" "$EMAIL_RECIPIENTS" 2>/dev/null || true
    fi
    
    # 短信通知（需要集成短信网关）
    if [ "$ENABLE_SMS_NOTIFICATION" = "true" ] && [ -n "$SMS_RECIPIENTS" ]; then
        # 这里可以集成企业短信网关
        log_strategy "短信通知功能需要集成短信网关"
    fi
    
    # 记录通知日志
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $status: $message" >> "$BACKUP_LOG_DIR/notifications.log"
}

# 检查存储使用情况
check_storage_usage() {
    log_strategy "检查存储使用情况..."
    
    local usage=$(df "$BACKUP_STORAGE_PATH" | awk 'NR==2 {print $5}' | tr -d '%')
    
    if [ "$usage" -gt "$MAX_STORAGE_USAGE" ]; then
        log_strategy_warning "存储使用率过高: ${usage}% (阈值: ${MAX_STORAGE_USAGE}%)"
        
        if [ "$AUTO_CLEANUP_ENABLED" = "true" ]; then
            log_strategy "启动自动清理..."
            cleanup_old_backups
        fi
        
        send_notification "WARNING" "备份存储使用率过高: ${usage}%"
    else
        log_strategy "存储使用率正常: ${usage}%"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_strategy "清理过期备份文件..."
    
    # 清理过期的在线备份
    if [ -n "$ONLINE_RETENTION_DAYS" ] && [ "$ONLINE_RETENTION_DAYS" -gt 0 ]; then
        find "$BACKUP_BASE_DIR/level0" -name "*.bkp" -mtime +$ONLINE_RETENTION_DAYS -delete 2>/dev/null || true
        find "$BACKUP_BASE_DIR/level1" -name "*.bkp" -mtime +$ONLINE_RETENTION_DAYS -delete 2>/dev/null || true
        log_strategy "已清理 $ONLINE_RETENTION_DAYS 天前的在线备份文件"
    fi
    
    # 清理过期的归档日志
    if [ -n "$ARCHIVELOG_RETENTION_DAYS" ] && [ "$ARCHIVELOG_RETENTION_DAYS" -gt 0 ]; then
        find "$BACKUP_BASE_DIR/archivelogs" -name "*.arc" -mtime +$ARCHIVELOG_RETENTION_DAYS -delete 2>/dev/null || true
        log_strategy "已清理 $ARCHIVELOG_RETENTION_DAYS 天前的归档日志文件"
    fi
    
    # 清理过期的日志文件
    find "$BACKUP_LOG_DIR" -name "*.log" -mtime +30 -delete 2>/dev/null || true
    log_strategy "已清理30天前的日志文件"
}

# =============================================================================
# 主函数和命令行接口
# =============================================================================

# 显示使用帮助
show_help() {
    cat <<EOF
Oracle 备份策略管理脚本

用法: $0 [选项] [命令]

命令:
  run                 执行备份策略
  config              显示当前配置
  validate            验证配置
  status              显示备份状态
  cleanup             清理过期备份
  help                显示此帮助信息

选项:
  --strategy TYPE     指定备份策略类型 (ONLINE_ONLY|OFFLINE_ONLY|HYBRID)
  --force             强制执行，忽略时间限制
  --dry-run           模拟执行，不实际运行备份

示例:
  $0 run                          # 执行配置的备份策略
  $0 --strategy ONLINE_ONLY run   # 强制执行在线备份策略
  $0 --dry-run run               # 模拟执行备份策略
  $0 config                      # 显示当前配置
  $0 status                      # 显示备份状态
  $0 cleanup                     # 清理过期备份

配置文件位置: $STRATEGY_CONFIG_FILE
日志文件位置: $STRATEGY_LOG_FILE
EOF
}

# 显示当前配置
show_config() {
    log_strategy "========================================="
    log_strategy "当前备份策略配置"
    log_strategy "========================================="
    
    if [ -f "$STRATEGY_CONFIG_FILE" ]; then
        cat "$STRATEGY_CONFIG_FILE"
    else
        log_strategy_error "配置文件不存在: $STRATEGY_CONFIG_FILE"
        return 1
    fi
}

# 显示备份状态
show_status() {
    log_strategy "========================================="
    log_strategy "备份状态信息"
    log_strategy "========================================="
    
    # 显示最近的备份信息
    log_strategy "最近的备份文件:"
    find "$BACKUP_BASE_DIR" -name "*.bkp" -type f -mtime -7 -exec ls -lh {} \; 2>/dev/null | head -10
    
    # 显示存储使用情况
    log_strategy "存储使用情况:"
    df -h "$BACKUP_BASE_DIR"
    
    # 显示RMAN备份信息
    log_strategy "RMAN备份信息:"
    rman target / <<EOF 2>/dev/null | head -20
LIST BACKUP SUMMARY;
EXIT;
EOF
}

# 主函数
main() {
    local command=""
    local force_run=false
    local dry_run=false
    local override_strategy=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --strategy)
                override_strategy="$2"
                shift 2
                ;;
            --force)
                force_run=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            run|config|validate|status|cleanup|help)
                command="$1"
                shift
                ;;
            *)
                log_strategy_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定命令，显示帮助
    if [ -z "$command" ]; then
        show_help
        exit 0
    fi
    
    # 检查锁文件
    if [ -f "$LOCK_FILE" ] && [ "$command" = "run" ]; then
        local lock_pid=$(cat "$LOCK_FILE" 2>/dev/null)
        if [ -n "$lock_pid" ] && kill -0 "$lock_pid" 2>/dev/null; then
            log_strategy_error "备份策略管理器已在运行中 (PID: $lock_pid)"
            exit 1
        else
            rm -f "$LOCK_FILE"
        fi
    fi
    
    # 创建锁文件
    if [ "$command" = "run" ]; then
        echo $$ > "$LOCK_FILE"
    fi
    
    log_strategy "========================================="
    log_strategy "Oracle 备份策略管理器启动"
    log_strategy "命令: $command"
    log_strategy "开始时间: $(date)"
    log_strategy "========================================="
    
    # 执行相应命令
    case "$command" in
        "run")
            read_config
            if ! validate_config; then
                exit 1
            fi
            
            # 覆盖策略设置
            if [ -n "$override_strategy" ]; then
                BACKUP_STRATEGY="$override_strategy"
                log_strategy "使用覆盖策略: $BACKUP_STRATEGY"
            fi
            
            if [ "$dry_run" = true ]; then
                log_strategy "模拟执行模式，不会实际运行备份"
                exit 0
            fi
            
            # 执行备份策略
            case "$BACKUP_STRATEGY" in
                "ONLINE_ONLY")
                    execute_online_backup_strategy
                    ;;
                "OFFLINE_ONLY")
                    execute_offline_backup_strategy
                    ;;
                "HYBRID")
                    execute_hybrid_backup_strategy
                    ;;
                *)
                    log_strategy_error "未知的备份策略: $BACKUP_STRATEGY"
                    exit 1
                    ;;
            esac
            
            # 检查存储使用情况
            check_storage_usage
            ;;
        "config")
            show_config
            ;;
        "validate")
            read_config
            validate_config
            ;;
        "status")
            show_status
            ;;
        "cleanup")
            read_config
            cleanup_old_backups
            ;;
        "help")
            show_help
            ;;
    esac
    
    log_strategy "========================================="
    log_strategy "备份策略管理器执行完成"
    log_strategy "结束时间: $(date)"
    log_strategy "========================================="
}

# 执行主函数
main "$@"
