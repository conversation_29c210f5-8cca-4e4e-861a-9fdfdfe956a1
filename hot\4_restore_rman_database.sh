#!/bin/bash

export ORACLE_SID=PDBQZ
BACKUP_DIR="/opt/oracle/rman_backup"
LOGFILE="$BACKUP_DIR/restore_$(date +%Y%m%d_%H%M%S).log"

echo "[$(date)] === START RMAN FULL RESTORE ===" | tee -a "$LOGFILE"

rman target / log="$LOGFILE" <<EOF
-- 指定备份文件所在目录
CATALOG START WITH '$BACKUP_DIR';

-- 检查可用备份
LIST BACKUP SUMMARY;

-- 启动数据库到 MOUNT 状态（如果尚未）
SHUTDOWN IMMEDIATE;
STARTUP MOUNT;

-- 恢复数据库（从 level 0 备份及归档日志）
RUN {
  RESTORE DATABASE;
  RECOVER DATABASE;
}

-- 打开数据库
ALTER DATABASE OPEN RESETLOGS;
EOF

echo "[$(date)] === END RMAN FULL RESTORE ===" | tee -a "$LOGFILE"
