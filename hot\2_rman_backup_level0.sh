#!/bin/bash

# 载入 Oracle 环境变量，如必要请取消注释
# source /home/<USER>/.bash_profile

# 定义备份目录和日志文件
BACKUP_DIR="/opt/oracle/rman_backup"
LOCK_FILE="/tmp/rman_backup_level0.lock"
LOGFILE="$BACKUP_DIR/full_level0_$(date +%Y%m%d).log"

# 创建备份目录（如不存在）
mkdir -p "$BACKUP_DIR"

# 防止重复运行
if [ -f "$LOCK_FILE" ]; then
  echo "[$(date)] Backup is already running. Exiting." | tee -a "$LOGFILE"
  exit 1
fi

# 创建锁文件
touch "$LOCK_FILE"

# 开始日志记录
echo "[$(date)] === RMAN LEVEL 0 BACKUP START ===" | tee -a "$LOGFILE"

# 执行 RMAN 备份任务
rman target / log="$LOGFILE" <<EOF
RUN {
  ALLOCATE CHANNEL c1 DEVICE TYPE DISK;
  ALLOCATE CHANNEL c2 DEVICE TYPE DISK;

  CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 30 DAYS;
  CONFIGURE ARCHIVELOG DELETION POLICY TO BACKED UP 1 TIMES TO DISK;

  CROSSCHECK BACKUP;
  DELETE NOPROMPT EXPIRED BACKUP;

  BACKUP INCREMENTAL LEVEL 0 AS COMPRESSED BACKUPSET DATABASE
    FORMAT '$BACKUP_DIR/level0_%d_%T_%U.bkp'
    TAG='LEVEL0_FULL_BACKUP';

  BACKUP ARCHIVELOG ALL DELETE INPUT
    FORMAT '$BACKUP_DIR/arch_%d_%T_%U.arc';

  DELETE NOPROMPT OBSOLETE;

  RELEASE CHANNEL c1;
  RELEASE CHANNEL c2;
}
EOF

# 结束日志记录
echo "[$(date)] === RMAN LEVEL 0 BACKUP END ===" | tee -a "$LOGFILE"

# 删除锁文件
rm -f "$LOCK_FILE"
