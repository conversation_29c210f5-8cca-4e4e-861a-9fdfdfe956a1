#!/bin/bash
source 0_oracle_env.sh

BACKUP_DIR=/opt/oracle/rman_backup
LOCK_FILE=/tmp/rman_backup_level1.lock
LOGFILE=$BACKUP_DIR/inc_level1_$(date +%Y%m%d).log

mkdir -p "$BACKUP_DIR"

# 防止重复运行
if [ -f "$LOCK_FILE" ]; then
  echo "[$(date)] Incremental backup is already running. Exiting." | tee -a "$LOGFILE"
  exit 1
fi

# 创建锁文件
touch "$LOCK_FILE"

echo "[$(date)] === RMAN LEVEL 1 INCREMENTAL BACKUP START ===" | tee -a "$LOGFILE"

rman target / log="$LOGFILE" <<EOF
RUN {
  ALLOCATE CHANNEL c1 DEVICE TYPE DISK;
  ALLOCATE CHANNEL c2 DEVICE TYPE DISK;

  CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 30 DAYS;

  CROSSCHECK BACKUP;
  DELETE NOPROMPT EXPIRED BACKUP;

  BACKUP INCREMENTAL LEVEL 1 AS COMPRESSED BACKUPSET DATABASE
    FORMAT '$BACKUP_DIR/level1_%d_%T_%U.bkp'
    TAG='LEVEL1_INC_BACKUP';

  BACKUP ARCHIVELOG ALL DELETE INPUT
    FORMAT '$BACKUP_DIR/arch_%d_%T_%U.arc';

  DELETE NOPROMPT OBSOLETE;

  RELEASE CHANNEL c1;
  RELEASE CHANNEL c2;
}
EOF

echo "[$(date)] === RMAN LEVEL 1 INCREMENTAL BACKUP END ===" | tee -a "$LOGFILE"

# 删除锁文件
rm -f "$LOCK_FILE"
