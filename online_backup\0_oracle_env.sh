#!/bin/bash

# Oracle 在线备份环境配置和检查脚本
# 基于离线备份的完善环境检查功能优化

# =============================================================================
# 环境变量配置
# =============================================================================

# Oracle基础环境变量
export ORACLE_SID=PDBQZ
export ORACLE_HOME=/opt/oracle/product/11gR2/db  # 修正为11gR2版本
export PATH=$ORACLE_HOME/bin:$PATH
export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH

# 备份相关环境变量
export BACKUP_BASE_DIR="/opt/oracle/rman_backup"
export ARCHIVE_LOG_DIR="/opt/oracle/archivelog"
export BACKUP_LOG_DIR="$BACKUP_BASE_DIR/logs"

# 创建必要的目录
mkdir -p "$BACKUP_BASE_DIR"
mkdir -p "$ARCHIVE_LOG_DIR"
mkdir -p "$BACKUP_LOG_DIR"

# =============================================================================
# 环境检查函数
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查Oracle环境
check_oracle_environment() {
    log_info "开始Oracle环境检查..."

    # 检查当前用户
    local current_user=$(whoami)
    log_info "当前用户: $current_user"

    if [ "$current_user" != "oracle" ]; then
        log_warning "建议使用oracle用户运行备份脚本"
    fi

    # 检查环境变量
    log_info "检查环境变量..."
    if [ -z "$ORACLE_HOME" ]; then
        log_error "ORACLE_HOME未设置"
        return 1
    else
        log_success "ORACLE_HOME: $ORACLE_HOME"
    fi

    if [ -z "$ORACLE_SID" ]; then
        log_error "ORACLE_SID未设置"
        return 1
    else
        log_success "ORACLE_SID: $ORACLE_SID"
    fi

    # 检查Oracle主目录
    if [ ! -d "$ORACLE_HOME" ]; then
        log_error "Oracle主目录不存在: $ORACLE_HOME"
        return 1
    else
        log_success "Oracle主目录存在: $ORACLE_HOME"
    fi

    # 检查Oracle二进制文件
    if [ ! -f "$ORACLE_HOME/bin/sqlplus" ]; then
        log_error "sqlplus不存在: $ORACLE_HOME/bin/sqlplus"
        return 1
    else
        log_success "sqlplus可用"
    fi

    if [ ! -f "$ORACLE_HOME/bin/rman" ]; then
        log_error "rman不存在: $ORACLE_HOME/bin/rman"
        return 1
    else
        log_success "rman可用"
    fi

    return 0
}

# 检查数据库状态
check_database_status() {
    log_info "检查数据库状态..."

    local db_status=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT STATUS FROM V\$INSTANCE;
EXIT;
EOF
)

    if [ $? -eq 0 ] && [ -n "$db_status" ]; then
        log_success "数据库实例状态: $db_status"

        # 检查数据库打开模式
        local open_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT OPEN_MODE FROM V\$DATABASE;
EXIT;
EOF
)
        if [ $? -eq 0 ] && [ -n "$open_mode" ]; then
            log_success "数据库打开模式: $open_mode"
        fi

        # 检查归档模式
        local archive_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT LOG_MODE FROM V\$DATABASE;
EXIT;
EOF
)
        if [ $? -eq 0 ] && [ -n "$archive_mode" ]; then
            if [ "$archive_mode" = "ARCHIVELOG" ]; then
                log_success "数据库归档模式: $archive_mode (适合在线备份)"
            else
                log_warning "数据库归档模式: $archive_mode (建议启用归档模式进行在线备份)"
            fi
        fi

        return 0
    else
        log_error "无法连接到数据库实例"
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."

    local backup_space=$(df -BG "$BACKUP_BASE_DIR" 2>/dev/null | awk 'NR==2 {print $4}' | tr -d 'G')
    if [ -n "$backup_space" ] && [ "$backup_space" -gt 0 ]; then
        if [ "$backup_space" -lt 20 ]; then
            log_error "备份目录可用空间不足: ${backup_space}GB (建议至少20GB)"
            return 1
        elif [ "$backup_space" -lt 50 ]; then
            log_warning "备份目录可用空间较少: ${backup_space}GB (建议至少50GB)"
        else
            log_success "备份目录可用空间: ${backup_space}GB"
        fi
    else
        log_error "无法检查备份目录磁盘空间"
        return 1
    fi

    return 0
}

# 检查备份目录权限
check_backup_directories() {
    log_info "检查备份目录..."

    local dirs=("$BACKUP_BASE_DIR" "$ARCHIVE_LOG_DIR" "$BACKUP_LOG_DIR")

    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_warning "目录不存在，正在创建: $dir"
            mkdir -p "$dir"
            if [ $? -eq 0 ]; then
                log_success "目录创建成功: $dir"
            else
                log_error "目录创建失败: $dir"
                return 1
            fi
        else
            log_success "目录存在: $dir"
        fi

        # 检查写权限
        if [ -w "$dir" ]; then
            log_success "目录可写: $dir"
        else
            log_error "目录不可写: $dir"
            return 1
        fi
    done

    return 0
}

# 检查RMAN配置
check_rman_configuration() {
    log_info "检查RMAN配置..."

    rman target / <<EOF > /tmp/rman_config_check.log 2>&1
SHOW ALL;
EXIT;
EOF

    if [ $? -eq 0 ]; then
        log_success "RMAN配置检查完成，详细信息请查看: /tmp/rman_config_check.log"

        # 检查关键配置
        if grep -q "RETENTION POLICY" /tmp/rman_config_check.log; then
            local retention=$(grep "RETENTION POLICY" /tmp/rman_config_check.log | head -1)
            log_info "保留策略: $retention"
        fi

        if grep -q "CONTROLFILE AUTOBACKUP" /tmp/rman_config_check.log; then
            local autobackup=$(grep "CONTROLFILE AUTOBACKUP" /tmp/rman_config_check.log | head -1)
            log_info "控制文件自动备份: $autobackup"
        fi

        return 0
    else
        log_error "RMAN配置检查失败"
        return 1
    fi
}

# 主环境检查函数
run_environment_check() {
    log_info "========================================="
    log_info "Oracle在线备份环境检查开始"
    log_info "========================================="

    local check_failed=0

    # 执行各项检查
    check_oracle_environment || check_failed=1
    echo

    check_database_status || check_failed=1
    echo

    check_disk_space || check_failed=1
    echo

    check_backup_directories || check_failed=1
    echo

    check_rman_configuration || check_failed=1
    echo

    # 显示系统信息
    log_info "系统信息:"
    log_info "主机名: $(hostname)"
    log_info "操作系统: $(uname -a)"
    log_info "当前时间: $(date)"
    log_info "运行目录: $(pwd)"

    echo
    log_info "========================================="
    if [ $check_failed -eq 0 ]; then
        log_success "环境检查全部通过，可以执行在线备份"
        log_info "========================================="
        return 0
    else
        log_error "环境检查发现问题，请修复后再执行备份"
        log_info "========================================="
        return 1
    fi
}

# =============================================================================
# 实用工具函数
# =============================================================================

# 显示环境信息
show_environment() {
    echo "========================================="
    echo "Oracle在线备份环境信息"
    echo "========================================="
    echo "ORACLE_HOME: $ORACLE_HOME"
    echo "ORACLE_SID: $ORACLE_SID"
    echo "备份基础目录: $BACKUP_BASE_DIR"
    echo "归档日志目录: $ARCHIVE_LOG_DIR"
    echo "备份日志目录: $BACKUP_LOG_DIR"
    echo "PATH: $PATH"
    echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
    echo "========================================="
}

# 如果直接运行此脚本，则执行环境检查
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    run_environment_check
    exit $?
fi

# 脚本被source时的提示信息
log_success "Oracle在线备份环境配置已加载"
log_info "使用 'run_environment_check' 命令执行完整环境检查"
log_info "使用 'show_environment' 命令显示环境变量信息"
