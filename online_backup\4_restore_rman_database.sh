#!/bin/bash

# Oracle 11gR2 RMAN 在线备份完整恢复脚本 - 企业级优化版
# 功能：从Level 0和Level 1增量备份完整恢复数据库
# 特点：智能备份选择、完整性验证、详细日志记录、安全恢复流程

# =============================================================================
# 脚本配置和环境加载
# =============================================================================

# 获取脚本目录并加载环境配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/0_oracle_env.sh"

# 恢复配置
RESTORE_DATE=$(date +%Y%m%d)
RESTORE_TIME=$(date +%H%M%S)
RESTORE_TIMESTAMP="${RESTORE_DATE}_${RESTORE_TIME}"
LOGFILE="$BACKUP_LOG_DIR/restore_database_$RESTORE_TIMESTAMP.log"
RESTORE_LOCK_FILE="/tmp/rman_restore.lock"

# 备份当前数据文件的目录
BACKUP_CURRENT_DATA_DIR="/backup/oradata_backup_before_restore_$RESTORE_TIMESTAMP"

# 创建必要目录
mkdir -p "$BACKUP_LOG_DIR"
mkdir -p "$BACKUP_CURRENT_DATA_DIR"

# =============================================================================
# 日志和工具函数
# =============================================================================

# 日志函数
log_restore() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOGFILE"
}

log_restore_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOGFILE" >&2
}

log_restore_success() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1" | tee -a "$LOGFILE"
}

log_restore_warning() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1" | tee -a "$LOGFILE"
}

# 清理函数
cleanup() {
    log_restore "执行清理操作..."
    rm -f "$RESTORE_LOCK_FILE"

    if [ $? -ne 0 ]; then
        log_restore_error "恢复过程中发生错误，请检查日志文件: $LOGFILE"
        echo "Oracle数据库恢复失败 - $(date)" >> "$BACKUP_LOG_DIR/restore_failures.log"
    fi
}

# 设置信号处理
trap cleanup EXIT INT TERM

# =============================================================================
# 前置检查函数
# =============================================================================

# 检查恢复前置条件
check_restore_prerequisites() {
    log_restore "========================================="
    log_restore "检查数据库恢复前置条件"
    log_restore "========================================="

    # 检查锁文件
    if [ -f "$RESTORE_LOCK_FILE" ]; then
        local lock_pid=$(cat "$RESTORE_LOCK_FILE" 2>/dev/null)
        if [ -n "$lock_pid" ] && kill -0 "$lock_pid" 2>/dev/null; then
            log_restore_error "数据库恢复已在运行中 (PID: $lock_pid)"
            return 1
        else
            log_restore "清理过期的锁文件"
            rm -f "$RESTORE_LOCK_FILE"
        fi
    fi

    # 创建锁文件
    echo $$ > "$RESTORE_LOCK_FILE"
    log_restore "创建锁文件: $RESTORE_LOCK_FILE (PID: $$)"

    # 检查Oracle环境
    if ! check_oracle_environment; then
        log_restore_error "Oracle环境检查失败"
        return 1
    fi

    # 检查是否为oracle用户
    if [ "$(whoami)" != "oracle" ]; then
        log_restore_warning "建议使用oracle用户执行恢复操作"
    fi

    # 检查磁盘空间
    if ! check_disk_space; then
        log_restore_error "磁盘空间检查失败"
        return 1
    fi

    # 检查备份文件是否存在
    if [ ! -d "$BACKUP_BASE_DIR" ]; then
        log_restore_error "备份目录不存在: $BACKUP_BASE_DIR"
        return 1
    fi

    # 检查Level 0备份
    local level0_count=$(find "$BACKUP_BASE_DIR/level0" -name "*.bkp" -type f 2>/dev/null | wc -l)
    if [ "$level0_count" -eq 0 ]; then
        log_restore_error "未找到Level 0备份文件"
        return 1
    else
        log_restore_success "找到 $level0_count 个Level 0备份文件"
    fi

    # 检查Level 1备份（可选）
    local level1_count=$(find "$BACKUP_BASE_DIR/level1" -name "*.bkp" -type f 2>/dev/null | wc -l)
    if [ "$level1_count" -gt 0 ]; then
        log_restore_success "找到 $level1_count 个Level 1增量备份文件"
    else
        log_restore_warning "未找到Level 1增量备份文件，将仅使用Level 0备份"
    fi

    # 检查控制文件备份
    local ctl_count=$(find "$BACKUP_BASE_DIR/controlfiles" -name "*.ctl" -type f 2>/dev/null | wc -l)
    if [ "$ctl_count" -eq 0 ]; then
        log_restore_error "未找到控制文件备份"
        return 1
    else
        log_restore_success "找到 $ctl_count 个控制文件备份"
    fi

    # 检查RMAN连接
    if ! rman target / <<< "EXIT;" > /dev/null 2>&1; then
        log_restore_error "无法连接到RMAN"
        return 1
    else
        log_restore_success "RMAN连接正常"
    fi

    log_restore "恢复前置条件检查完成"
    return 0
}

# 显示重要警告
show_restore_warnings() {
    log_restore "========================================="
    log_restore "重要警告和确认"
    log_restore "========================================="

    echo "========================================="
    echo "重要警告："
    echo "1. 此操作将完全恢复数据库到备份时点"
    echo "2. 当前数据库中的所有数据将被替换"
    echo "3. 恢复后的数据将丢失备份时点之后的所有更改"
    echo "4. 建议在维护窗口期间执行"
    echo "5. 确保已通知所有相关用户"
    echo "========================================="
    echo "恢复信息："
    echo "- 备份目录: $BACKUP_BASE_DIR"
    echo "- 当前数据备份到: $BACKUP_CURRENT_DATA_DIR"
    echo "- 恢复日志: $LOGFILE"
    echo "========================================="
    echo "是否继续执行数据库恢复？(输入 'YES' 确认)"

    read -r response
    if [ "$response" != "YES" ]; then
        log_restore "用户取消恢复操作"
        echo "恢复操作已取消"
        return 1
    fi

    log_restore "用户确认执行数据库恢复"
    return 0
}

# 分析可用备份
analyze_available_backups() {
    log_restore "========================================="
    log_restore "分析可用备份"
    log_restore "========================================="

    local backup_analysis_file="$BACKUP_LOG_DIR/backup_analysis_$RESTORE_TIMESTAMP.txt"

    # 使用RMAN分析备份
    rman target / <<EOF > "$backup_analysis_file" 2>&1
CATALOG START WITH '$BACKUP_BASE_DIR';
CROSSCHECK BACKUP;
LIST BACKUP SUMMARY;
LIST BACKUP BY FILE;
REPORT SCHEMA;
EXIT;
EOF

    if [ $? -eq 0 ]; then
        log_restore_success "备份分析完成，详细信息保存到: $backup_analysis_file"

        # 显示关键备份信息
        if grep -q "LEVEL 0" "$backup_analysis_file"; then
            local latest_level0=$(grep "LEVEL 0" "$backup_analysis_file" | tail -1)
            log_restore "最新Level 0备份: $latest_level0"
        fi

        if grep -q "LEVEL 1" "$backup_analysis_file"; then
            local latest_level1=$(grep "LEVEL 1" "$backup_analysis_file" | tail -1)
            log_restore "最新Level 1备份: $latest_level1"
        fi

        return 0
    else
        log_restore_error "备份分析失败"
        return 1
    fi
}

# 备份当前数据文件
backup_current_datafiles() {
    log_restore "========================================="
    log_restore "备份当前数据文件"
    log_restore "========================================="

    log_restore "备份当前数据文件到: $BACKUP_CURRENT_DATA_DIR"

    # 获取数据文件路径
    local datafile_paths=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT FILE_NAME FROM DBA_DATA_FILES;
EXIT;
EOF
)

    if [ $? -eq 0 ] && [ -n "$datafile_paths" ]; then
        # 备份数据文件
        echo "$datafile_paths" | while read -r datafile; do
            if [ -f "$datafile" ]; then
                local backup_path="$BACKUP_CURRENT_DATA_DIR/$(basename "$datafile")"
                log_restore "备份数据文件: $datafile -> $backup_path"
                cp "$datafile" "$backup_path" 2>/dev/null || log_restore_warning "无法备份数据文件: $datafile"
            fi
        done

        # 备份控制文件
        sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
ALTER DATABASE BACKUP CONTROLFILE TO '$BACKUP_CURRENT_DATA_DIR/control_backup_before_restore.ctl';
ALTER DATABASE BACKUP CONTROLFILE TO TRACE AS '$BACKUP_CURRENT_DATA_DIR/create_controlfile_before_restore.sql';
EOF

        log_restore_success "当前数据文件备份完成"
        return 0
    else
        log_restore_error "无法获取数据文件信息"
        return 1
    fi
}

# =============================================================================
# 核心恢复函数
# =============================================================================

# 执行数据库恢复
execute_database_restore() {
    log_restore "========================================="
    log_restore "开始执行数据库恢复"
    log_restore "========================================="

    # 关闭数据库
    log_restore "关闭数据库..."
    sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SHUTDOWN IMMEDIATE;
EXIT;
EOF

    if [ $? -ne 0 ]; then
        log_restore_warning "数据库关闭可能有问题，继续执行恢复"
    fi

    # 启动到NOMOUNT状态
    log_restore "启动数据库到NOMOUNT状态..."
    sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
STARTUP NOMOUNT;
EXIT;
EOF

    if [ $? -ne 0 ]; then
        log_restore_error "无法启动数据库到NOMOUNT状态"
        return 1
    fi

    # 执行RMAN恢复
    log_restore "执行RMAN完整恢复..."
    rman target / log="$LOGFILE" <<EOF
RUN {
  # 分配多个通道以提高恢复性能
  ALLOCATE CHANNEL c1 DEVICE TYPE DISK;
  ALLOCATE CHANNEL c2 DEVICE TYPE DISK;
  ALLOCATE CHANNEL c3 DEVICE TYPE DISK;
  ALLOCATE CHANNEL c4 DEVICE TYPE DISK;

  # 设置备份位置
  SET NEWNAME FOR DATABASE TO NEW;

  # 恢复控制文件
  RESTORE CONTROLFILE FROM AUTOBACKUP;

  # 启动到MOUNT状态
  SQL "ALTER DATABASE MOUNT";

  # 恢复数据库
  RESTORE DATABASE;

  # 恢复数据库（应用增量备份和归档日志）
  RECOVER DATABASE;

  # 释放通道
  RELEASE CHANNEL c1;
  RELEASE CHANNEL c2;
  RELEASE CHANNEL c3;
  RELEASE CHANNEL c4;
}
EXIT;
EOF

    local rman_exit_code=$?

    if [ $rman_exit_code -eq 0 ]; then
        log_restore_success "RMAN数据库恢复完成"
        return 0
    else
        log_restore_error "RMAN数据库恢复失败，退出代码: $rman_exit_code"
        return 1
    fi
}

# 打开数据库
open_database() {
    log_restore "========================================="
    log_restore "打开数据库"
    log_restore "========================================="

    # 尝试正常打开数据库
    log_restore "尝试打开数据库..."
    sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
ALTER DATABASE OPEN;
EXIT;
EOF

    if [ $? -eq 0 ]; then
        log_restore_success "数据库正常打开"
        return 0
    else
        log_restore_warning "数据库无法正常打开，尝试RESETLOGS方式"

        # 使用RESETLOGS打开数据库
        sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
ALTER DATABASE OPEN RESETLOGS;
EXIT;
EOF

        if [ $? -eq 0 ]; then
            log_restore_success "数据库使用RESETLOGS方式打开"
            log_restore_warning "注意：使用RESETLOGS打开数据库，之前的归档日志将无效"
            return 0
        else
            log_restore_error "数据库无法打开"
            return 1
        fi
    fi
}

# 验证恢复结果
verify_restore_result() {
    log_restore "========================================="
    log_restore "验证数据库恢复结果"
    log_restore "========================================="

    # 检查数据库状态
    local db_status=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT STATUS FROM V\$INSTANCE;
EXIT;
EOF
)

    if [ "$db_status" = "OPEN" ]; then
        log_restore_success "数据库状态: $db_status"
    else
        log_restore_error "数据库状态异常: $db_status"
        return 1
    fi

    # 检查数据库打开模式
    local open_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT OPEN_MODE FROM V\$DATABASE;
EXIT;
EOF
)

    log_restore "数据库打开模式: $open_mode"

    # 检查数据文件状态
    log_restore "检查数据文件状态..."
    sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SET PAGESIZE 1000 LINESIZE 200
SELECT FILE_ID, FILE_NAME, TABLESPACE_NAME, STATUS FROM DBA_DATA_FILES ORDER BY FILE_ID;
EXIT;
EOF

    # 检查表空间状态
    log_restore "检查表空间状态..."
    local offline_ts=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT COUNT(*) FROM DBA_TABLESPACES WHERE STATUS != 'ONLINE';
EXIT;
EOF
)

    if [ "$offline_ts" -eq 0 ]; then
        log_restore_success "所有表空间状态正常"
    else
        log_restore_warning "发现 $offline_ts 个非在线表空间"
    fi

    # 执行基本数据完整性检查
    log_restore "执行基本数据完整性检查..."
    sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SELECT 'User Count: ' || COUNT(*) FROM DBA_USERS;
SELECT 'Table Count: ' || COUNT(*) FROM DBA_TABLES;
SELECT 'Index Count: ' || COUNT(*) FROM DBA_INDEXES;
EXIT;
EOF

    log_restore_success "数据库恢复验证完成"
    return 0
}

# 生成恢复报告
generate_restore_report() {
    log_restore "========================================="
    log_restore "生成数据库恢复报告"
    log_restore "========================================="

    local report_file="$BACKUP_LOG_DIR/database_restore_report_$RESTORE_TIMESTAMP.txt"

    cat > "$report_file" <<EOF
Oracle Database Restore Report
==============================
Restore Date: $RESTORE_DATE
Restore Time: $RESTORE_TIME
Oracle SID: $ORACLE_SID
Oracle Home: $ORACLE_HOME

Restore Process:
- Backup Source: $BACKUP_BASE_DIR
- Current Data Backup: $BACKUP_CURRENT_DATA_DIR
- Restore Log: $LOGFILE

Database Information After Restore:
EOF

    # 添加数据库信息
    sqlplus -s / as sysdba <<EOF >> "$report_file" 2>&1
SET PAGESIZE 1000 LINESIZE 200
SELECT 'Database Name: ' || NAME FROM V\$DATABASE;
SELECT 'Database ID: ' || DBID FROM V\$DATABASE;
SELECT 'Instance Status: ' || STATUS FROM V\$INSTANCE;
SELECT 'Database Open Mode: ' || OPEN_MODE FROM V\$DATABASE;
SELECT 'Archive Mode: ' || LOG_MODE FROM V\$DATABASE;
SELECT 'Database Size: ' || ROUND(SUM(BYTES)/1024/1024/1024,2) || ' GB' FROM DBA_DATA_FILES;
SELECT 'Tablespace Count: ' || COUNT(*) FROM DBA_TABLESPACES;
SELECT 'User Count: ' || COUNT(*) FROM DBA_USERS;
SELECT 'Table Count: ' || COUNT(*) FROM DBA_TABLES;
EXIT;
EOF

    echo -e "\nRestore Completion Time: $(date)" >> "$report_file"
    echo -e "Restore Duration: Calculated in main function" >> "$report_file"

    # 添加重要提醒
    cat >> "$report_file" <<EOF

Important Notes:
1. Database has been restored to the backup point-in-time
2. All changes after the backup time have been lost
3. If RESETLOGS was used, previous archive logs are invalid
4. Recommend full backup after successful restore
5. Verify application functionality before production use

Next Steps:
1. Test database connectivity
2. Verify critical application functions
3. Perform full database backup
4. Update backup schedule if needed
5. Monitor database performance

Files Preserved:
- Original data files backed up to: $BACKUP_CURRENT_DATA_DIR
- Detailed restore log: $LOGFILE
- Backup analysis: $BACKUP_LOG_DIR/backup_analysis_$RESTORE_TIMESTAMP.txt
EOF

    log_restore "数据库恢复报告已生成: $report_file"
}

# 发送恢复通知
send_restore_notification() {
    local status=$1
    local message=$2

    log_restore "恢复通知: $status - $message"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Database Restore $status: $message" >> "$BACKUP_LOG_DIR/restore_notifications.log"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_restore "========================================="
    log_restore "Oracle Database 在线备份恢复开始"
    log_restore "开始时间: $(date)"
    log_restore "执行用户: $(whoami)"
    log_restore "脚本路径: $0"
    log_restore "进程ID: $$"
    log_restore "日志文件: $LOGFILE"
    log_restore "========================================="

    local restore_start_time=$(date +%s)
    local restore_failed=0

    # 检查恢复前置条件
    if ! check_restore_prerequisites; then
        log_restore_error "恢复前置条件检查失败"
        send_restore_notification "FAILED" "前置条件检查失败"
        exit 1
    fi

    # 显示警告并确认
    if ! show_restore_warnings; then
        log_restore "用户取消恢复操作"
        exit 0
    fi

    # 分析可用备份
    if ! analyze_available_backups; then
        log_restore_error "备份分析失败"
        restore_failed=1
    fi

    # 备份当前数据文件
    if [ $restore_failed -eq 0 ]; then
        if ! backup_current_datafiles; then
            log_restore_error "当前数据文件备份失败"
            restore_failed=1
        fi
    fi

    # 执行数据库恢复
    if [ $restore_failed -eq 0 ]; then
        if ! execute_database_restore; then
            log_restore_error "数据库恢复失败"
            restore_failed=1
        fi
    fi

    # 打开数据库
    if [ $restore_failed -eq 0 ]; then
        if ! open_database; then
            log_restore_error "数据库打开失败"
            restore_failed=1
        fi
    fi

    # 验证恢复结果
    if [ $restore_failed -eq 0 ]; then
        if ! verify_restore_result; then
            log_restore_error "恢复结果验证失败"
            restore_failed=1
        fi
    fi

    # 生成恢复报告
    generate_restore_report

    # 计算恢复耗时
    local restore_end_time=$(date +%s)
    local restore_duration=$((restore_end_time - restore_start_time))
    local restore_duration_formatted=$(printf "%02d:%02d:%02d" $((restore_duration/3600)) $((restore_duration%3600/60)) $((restore_duration%60)))

    # 更新报告中的恢复时间
    sed -i "s/Restore Duration: Calculated in main function/Restore Duration: $restore_duration_formatted/" "$BACKUP_LOG_DIR/database_restore_report_$RESTORE_TIMESTAMP.txt" 2>/dev/null

    # 最终状态报告
    log_restore "========================================="
    if [ $restore_failed -eq 0 ]; then
        log_restore_success "Oracle Database 在线备份恢复成功完成"
        log_restore "完成时间: $(date)"
        log_restore "恢复耗时: $restore_duration_formatted"
        log_restore "原数据备份: $BACKUP_CURRENT_DATA_DIR"
        log_restore "恢复日志: $LOGFILE"
        log_restore "恢复报告: $BACKUP_LOG_DIR/database_restore_report_$RESTORE_TIMESTAMP.txt"

        send_restore_notification "SUCCESS" "数据库恢复成功完成，耗时: $restore_duration_formatted"

        # 显示数据库状态
        local db_info=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT 'Database: ' || NAME || ', Status: ' || (SELECT STATUS FROM V\$INSTANCE) || ', Mode: ' || OPEN_MODE FROM V\$DATABASE;
EXIT;
EOF
)
        log_restore "数据库状态: $db_info"

    else
        log_restore_error "Oracle Database 在线备份恢复失败"
        log_restore "失败时间: $(date)"
        log_restore "恢复耗时: $restore_duration_formatted"
        log_restore "错误日志: $LOGFILE"
        log_restore "原数据备份: $BACKUP_CURRENT_DATA_DIR"

        send_restore_notification "FAILED" "数据库恢复失败，请检查日志: $LOGFILE"

        echo "========================================="
        echo "数据库恢复失败！"
        echo "原始数据文件已备份到: $BACKUP_CURRENT_DATA_DIR"
        echo "可以尝试手动恢复或联系数据库管理员"
        echo "详细错误日志: $LOGFILE"
        echo "========================================="

        exit 1
    fi

    log_restore "========================================="

    # 显示成功消息到控制台
    echo "========================================="
    echo "Oracle Database 在线备份恢复成功完成！"
    echo "恢复时间: $restore_duration_formatted"
    echo "数据库状态: $(sqlplus -s / as sysdba <<< "SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF; SELECT STATUS FROM V\$INSTANCE; EXIT;")"
    echo "详细日志: $LOGFILE"
    echo "恢复报告: $BACKUP_LOG_DIR/database_restore_report_$RESTORE_TIMESTAMP.txt"
    echo "原数据备份: $BACKUP_CURRENT_DATA_DIR"
    echo "========================================="
    echo "重要提醒："
    echo "1. 请验证应用程序功能是否正常"
    echo "2. 建议执行完整备份"
    echo "3. 监控数据库性能"
    echo "4. 如有问题，原数据文件已保存"
    echo "========================================="
}

# 执行主函数
main "$@"
