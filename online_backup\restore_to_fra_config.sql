-- =====================================================================
-- 恢复到原来的FRA归档配置
-- 功能：将归档配置恢复到使用Flash Recovery Area
-- 适用：希望恢复到原来配置的情况
-- =====================================================================

-- 设置SQL*Plus环境
SET ECHO ON
SET FEEDBACK ON
SET PAGESIZE 1000
SET LINESIZE 200
SET TIMING ON

PROMPT =====================================================================
PROMPT 恢复到Flash Recovery Area归档配置
PROMPT =====================================================================

-- 显示当前配置
PROMPT 当前归档配置:
SELECT 
    DEST_ID,
    DESTINATION,
    STATUS
FROM V$ARCHIVE_DEST 
WHERE DEST_ID <= 2 AND STATUS != 'INACTIVE';

-- 显示FRA配置
SELECT 
    'FRA Location: ' || VALUE AS FRA_CONFIG
FROM V$PARAMETER 
WHERE NAME = 'db_recovery_file_dest';

SELECT 
    'FRA Size: ' || VALUE AS FRA_SIZE
FROM V$PARAMETER 
WHERE NAME = 'db_recovery_file_dest_size';

-- 备份当前参数文件
PROMPT =====================================================================
PROMPT 备份当前参数文件
PROMPT =====================================================================
CREATE PFILE='/opt/oracle/rman_backup/init_before_fra_restore.ora' FROM SPFILE;
PROMPT 参数文件已备份到: /opt/oracle/rman_backup/init_before_fra_restore.ora

-- 检查FRA是否已配置
DECLARE
    v_fra_dest VARCHAR2(512);
    v_fra_size VARCHAR2(100);
BEGIN
    SELECT VALUE INTO v_fra_dest FROM V$PARAMETER WHERE NAME = 'db_recovery_file_dest';
    SELECT VALUE INTO v_fra_size FROM V$PARAMETER WHERE NAME = 'db_recovery_file_dest_size';
    
    IF v_fra_dest IS NULL OR v_fra_size IS NULL OR v_fra_size = '0' THEN
        DBMS_OUTPUT.PUT_LINE('警告: FRA未正确配置，需要先配置FRA');
        DBMS_OUTPUT.PUT_LINE('FRA Location: ' || NVL(v_fra_dest, 'NOT SET'));
        DBMS_OUTPUT.PUT_LINE('FRA Size: ' || NVL(v_fra_size, 'NOT SET'));
    ELSE
        DBMS_OUTPUT.PUT_LINE('FRA配置正常');
        DBMS_OUTPUT.PUT_LINE('FRA Location: ' || v_fra_dest);
        DBMS_OUTPUT.PUT_LINE('FRA Size: ' || v_fra_size);
    END IF;
END;
/

-- 如果FRA未配置，先配置FRA（根据原来的路径）
PROMPT =====================================================================
PROMPT 确保FRA配置正确
PROMPT =====================================================================

-- 设置FRA位置和大小（根据您原来的配置调整）
ALTER SYSTEM SET db_recovery_file_dest='/opt/oracle/flash_recovery_area' SCOPE=SPFILE;
ALTER SYSTEM SET db_recovery_file_dest_size=20G SCOPE=SPFILE;

-- 修改归档目标回到FRA
PROMPT =====================================================================
PROMPT 修改归档目标回到FRA
PROMPT =====================================================================

-- 将主归档目标改回FRA
ALTER SYSTEM SET log_archive_dest_1='LOCATION=USE_DB_RECOVERY_FILE_DEST VALID_FOR=(ALL_LOGFILES,ALL_ROLES) DB_UNIQUE_NAME=PDBQZ' SCOPE=SPFILE;
ALTER SYSTEM SET log_archive_dest_state_1='ENABLE' SCOPE=SPFILE;

-- 禁用自定义目录（如果有配置为第二个目标）
ALTER SYSTEM SET log_archive_dest_state_2='DEFER' SCOPE=SPFILE;

-- 重启数据库使配置生效
PROMPT =====================================================================
PROMPT 重启数据库使配置生效
PROMPT =====================================================================
PROMPT 注意：需要重启数据库才能使SPFILE中的FRA配置生效
PROMPT 请手动执行以下命令：
PROMPT SHUTDOWN IMMEDIATE;
PROMPT STARTUP;

-- 验证配置（重启后执行）
PROMPT =====================================================================
PROMPT 重启后验证配置
PROMPT =====================================================================
PROMPT 重启数据库后，请执行以下验证：

PROMPT -- 1. 检查归档目标
PROMPT SELECT DEST_ID, DESTINATION, STATUS FROM V$ARCHIVE_DEST WHERE DEST_ID <= 2;

PROMPT -- 2. 检查FRA配置
PROMPT SELECT * FROM V$RECOVERY_FILE_DEST;

PROMPT -- 3. 测试日志切换
PROMPT ALTER SYSTEM SWITCH LOGFILE;

PROMPT -- 4. 检查新归档日志位置
PROMPT SELECT NAME FROM V$ARCHIVED_LOG WHERE SEQUENCE# = (SELECT MAX(SEQUENCE#) FROM V$ARCHIVED_LOG);

PROMPT =====================================================================
PROMPT 配置恢复完成
PROMPT 重要提醒：
PROMPT 1. 必须重启数据库才能使FRA配置生效
PROMPT 2. 重启后验证归档日志确实写入FRA
PROMPT 3. 可以考虑清理 /opt/oracle/archivelog 中的旧文件
PROMPT 4. 更新备份脚本以适应FRA配置
PROMPT =====================================================================

EXIT;
