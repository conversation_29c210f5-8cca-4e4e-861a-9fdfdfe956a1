-- =====================================================================
-- 实施混合归档配置
-- 功能：同时使用FRA和自定义目录，获得双重保护
-- 适用：希望获得最佳可靠性的情况
-- =====================================================================

-- 设置SQL*Plus环境
SET ECHO ON
SET FEEDBACK ON
SET PAGESIZE 1000
SET LINESIZE 200
SET TIMING ON

PROMPT =====================================================================
PROMPT 实施混合归档配置 (FRA + 自定义目录)
PROMPT =====================================================================

-- 显示当前配置
PROMPT 当前配置状态:
SELECT 
    DEST_ID,
    DESTINATION,
    STATUS,
    BINDING
FROM V$ARCHIVE_DEST 
WHERE DEST_ID <= 5 AND STATUS != 'INACTIVE';

-- 备份当前参数文件
PROMPT =====================================================================
PROMPT 备份当前参数文件
PROMPT =====================================================================
CREATE PFILE='/opt/oracle/rman_backup/init_before_hybrid.ora' FROM SPFILE;
PROMPT 参数文件已备份到: /opt/oracle/rman_backup/init_before_hybrid.ora

-- 配置FRA（如果未配置或需要调整）
PROMPT =====================================================================
PROMPT 配置Flash Recovery Area
PROMPT =====================================================================

-- 检查并设置FRA
DECLARE
    v_fra_dest VARCHAR2(512);
    v_fra_size VARCHAR2(100);
BEGIN
    SELECT VALUE INTO v_fra_dest FROM V$PARAMETER WHERE NAME = 'db_recovery_file_dest';
    SELECT VALUE INTO v_fra_size FROM V$PARAMETER WHERE NAME = 'db_recovery_file_dest_size';
    
    DBMS_OUTPUT.PUT_LINE('当前FRA配置:');
    DBMS_OUTPUT.PUT_LINE('Location: ' || NVL(v_fra_dest, 'NOT SET'));
    DBMS_OUTPUT.PUT_LINE('Size: ' || NVL(v_fra_size, 'NOT SET'));
    
    IF v_fra_dest IS NULL OR v_fra_dest = '' THEN
        DBMS_OUTPUT.PUT_LINE('需要配置FRA位置');
    END IF;
    
    IF v_fra_size IS NULL OR v_fra_size = '0' THEN
        DBMS_OUTPUT.PUT_LINE('需要配置FRA大小');
    END IF;
END;
/

-- 设置FRA配置
ALTER SYSTEM SET db_recovery_file_dest='/opt/oracle/flash_recovery_area' SCOPE=SPFILE;
ALTER SYSTEM SET db_recovery_file_dest_size=30G SCOPE=SPFILE;

-- 配置混合归档目标
PROMPT =====================================================================
PROMPT 配置混合归档目标
PROMPT =====================================================================

-- 目标1: FRA (主要目标，自动管理)
ALTER SYSTEM SET log_archive_dest_1='LOCATION=USE_DB_RECOVERY_FILE_DEST VALID_FOR=(ALL_LOGFILES,ALL_ROLES) DB_UNIQUE_NAME=PDBQZ' SCOPE=SPFILE;
ALTER SYSTEM SET log_archive_dest_state_1='ENABLE' SCOPE=SPFILE;

-- 目标2: 自定义目录 (辅助目标，快速访问)
ALTER SYSTEM SET log_archive_dest_2='LOCATION=/opt/oracle/archivelog VALID_FOR=(ALL_LOGFILES,ALL_ROLES) DB_UNIQUE_NAME=PDBQZ' SCOPE=SPFILE;
ALTER SYSTEM SET log_archive_dest_state_2='ENABLE' SCOPE=SPFILE;

-- 设置归档格式
ALTER SYSTEM SET log_archive_format='arch_%t_%s_%r.arc' SCOPE=SPFILE;

-- 设置最小成功归档目标数（至少一个成功即可）
ALTER SYSTEM SET log_archive_min_succeed_dest=1 SCOPE=SPFILE;

-- 配置RMAN策略
PROMPT =====================================================================
PROMPT 配置RMAN策略
PROMPT =====================================================================

-- 使用RMAN配置保留策略
RMAN TARGET / <<RMANEOF
CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 7 DAYS;
CONFIGURE ARCHIVELOG DELETION POLICY TO BACKED UP 1 TIMES TO DISK;
CONFIGURE BACKUP OPTIMIZATION ON;
CONFIGURE CONTROLFILE AUTOBACKUP ON;
EXIT;
RMANEOF

-- 重启提醒
PROMPT =====================================================================
PROMPT 重启数据库使配置生效
PROMPT =====================================================================
PROMPT 注意：需要重启数据库才能使新的FRA配置生效
PROMPT 
PROMPT 请执行以下步骤：
PROMPT 1. SHUTDOWN IMMEDIATE;
PROMPT 2. STARTUP;
PROMPT 3. 验证配置是否正确

-- 重启后的验证脚本
PROMPT =====================================================================
PROMPT 重启后验证脚本
PROMPT =====================================================================
PROMPT 重启数据库后，请执行以下验证：

PROMPT -- 1. 检查归档目标配置
PROMPT SELECT DEST_ID, DESTINATION, STATUS, BINDING FROM V$ARCHIVE_DEST WHERE DEST_ID <= 2;

PROMPT -- 2. 检查FRA状态
PROMPT SELECT * FROM V$RECOVERY_FILE_DEST;

PROMPT -- 3. 测试日志切换
PROMPT ALTER SYSTEM SWITCH LOGFILE;

PROMPT -- 4. 等待几秒后检查归档文件
PROMPT -- 应该在两个位置都能看到新的归档文件：
PROMPT -- /opt/oracle/flash_recovery_area/PDBQZ/archivelog/
PROMPT -- /opt/oracle/archivelog/

PROMPT -- 5. 检查最新归档日志位置
PROMPT SELECT DEST_ID, NAME FROM V$ARCHIVED_LOG WHERE SEQUENCE# = (SELECT MAX(SEQUENCE#) FROM V$ARCHIVED_LOG);

-- 混合模式的优势说明
PROMPT =====================================================================
PROMPT 混合模式配置完成
PROMPT =====================================================================
PROMPT 混合模式的优势：
PROMPT 1. 双重保护：归档日志同时写入两个位置
PROMPT 2. 自动管理：FRA提供自动空间管理
PROMPT 3. 快速访问：自定义目录便于脚本处理
PROMPT 4. 高可用性：一个位置故障不影响归档
PROMPT 
PROMPT 注意事项：
PROMPT 1. 需要双倍的归档存储空间
PROMPT 2. 写入性能可能略有影响
PROMPT 3. 需要监控两个位置的空间使用
PROMPT 4. 备份脚本会自动处理两个位置的归档日志
PROMPT 
PROMPT 管理建议：
PROMPT 1. 主要依赖FRA的自动管理功能
PROMPT 2. 自定义目录作为快速访问和应急备份
PROMPT 3. 定期检查两个位置的空间使用情况
PROMPT 4. RMAN备份会自动处理所有归档日志
PROMPT =====================================================================

EXIT;
