#!/bin/bash

# 检查当前归档配置状态脚本
# 用于分析归档配置变更后的当前状态

echo "========================================="
echo "Oracle 归档配置状态检查"
echo "开始时间: $(date)"
echo "========================================="

# 检查当前归档目标配置
echo ""
echo "=== 当前归档目标配置 ==="
sqlplus -s / as sysdba <<EOF
SET PAGESIZE 1000 LINESIZE 200
SELECT 
    DEST_ID,
    DESTINATION,
    STATUS,
    BINDING,
    VALID_FOR,
    ERROR
FROM V\$ARCHIVE_DEST 
WHERE DEST_ID <= 5 
ORDER BY DEST_ID;
EOF

# 检查FRA配置
echo ""
echo "=== Flash Recovery Area 配置 ==="
sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT 'FRA Location: ' || NVL(VALUE, 'NOT SET') FROM V\$PARAMETER WHERE NAME = 'db_recovery_file_dest';
SELECT 'FRA Size: ' || NVL(VALUE, 'NOT SET') FROM V\$PARAMETER WHERE NAME = 'db_recovery_file_dest_size';
EOF

# 检查FRA使用情况
echo ""
echo "=== FRA 使用情况 ==="
sqlplus -s / as sysdba <<EOF
SET PAGESIZE 1000 LINESIZE 200
SELECT 
    SPACE_LIMIT/1024/1024/1024 AS SPACE_LIMIT_GB,
    SPACE_USED/1024/1024/1024 AS SPACE_USED_GB,
    SPACE_RECLAIMABLE/1024/1024/1024 AS SPACE_RECLAIMABLE_GB,
    NUMBER_OF_FILES,
    ROUND((SPACE_USED/SPACE_LIMIT)*100,2) AS PCT_USED
FROM V\$RECOVERY_FILE_DEST;
EOF

# 检查归档日志文件分布
echo ""
echo "=== 归档日志文件分布 ==="
echo "检查 /opt/oracle/flash_recovery_area 目录："
if [ -d "/opt/oracle/flash_recovery_area" ]; then
    find /opt/oracle/flash_recovery_area -name "*.arc" -type f | wc -l | xargs echo "FRA中的归档文件数量:"
    find /opt/oracle/flash_recovery_area -name "*.arc" -type f -exec ls -lh {} \; | tail -5
else
    echo "FRA目录不存在"
fi

echo ""
echo "检查 /opt/oracle/archivelog 目录："
if [ -d "/opt/oracle/archivelog" ]; then
    find /opt/oracle/archivelog -name "*.arc" -type f | wc -l | xargs echo "自定义目录中的归档文件数量:"
    find /opt/oracle/archivelog -name "*.arc" -type f -exec ls -lh {} \; | tail -5
else
    echo "自定义归档目录不存在"
fi

# 检查最近的归档日志
echo ""
echo "=== 最近的归档日志 ==="
sqlplus -s / as sysdba <<EOF
SET PAGESIZE 1000 LINESIZE 200
SELECT 
    SEQUENCE#,
    FIRST_TIME,
    NAME,
    DEST_ID,
    ARCHIVED,
    APPLIED,
    DELETED
FROM V\$ARCHIVED_LOG 
WHERE FIRST_TIME > SYSDATE - 1
ORDER BY SEQUENCE# DESC;
EOF

# 检查当前日志状态
echo ""
echo "=== 当前重做日志状态 ==="
sqlplus -s / as sysdba <<EOF
SET PAGESIZE 1000 LINESIZE 200
SELECT 
    GROUP#,
    THREAD#,
    SEQUENCE#,
    BYTES/1024/1024 AS SIZE_MB,
    STATUS,
    ARCHIVED
FROM V\$LOG
ORDER BY GROUP#;
EOF

echo ""
echo "========================================="
echo "状态检查完成: $(date)"
echo "========================================="
