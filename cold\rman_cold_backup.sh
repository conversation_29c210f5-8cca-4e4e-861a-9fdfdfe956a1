#!/bin/bash

# Oracle 11gR2 完整冷备份脚本 - 优化版
# 包含数据库、控制文件、参数文件、归档日志、密码文件等完整备份

# 定义变量（与还原脚本保持一致）
BACKUP_DIR="/opt/oracle/rman_backup"
BACKUP_DATE=$(date +%Y%m%d)
LOGFILE="$BACKUP_DIR/cold_backup_$BACKUP_DATE.log"
ORACLE_HOME="/opt/oracle/product/11gR2/db"
ORACLE_SID="PDBQZ"

# 设置环境变量
export ORACLE_HOME=$ORACLE_HOME
export ORACLE_SID=$ORACLE_SID
export PATH=$ORACLE_HOME/bin:$PATH
export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH

# 创建备份目录（简化结构，与还原脚本兼容）
mkdir -p "$BACKUP_DIR"
mkdir -p "$BACKUP_DIR/config"

# 备份开始日志
echo "[$(date)] ========================================" | tee -a "$LOGFILE"
echo "[$(date)] === ORACLE COMPLETE COLD BACKUP START ===" | tee -a "$LOGFILE"
echo "[$(date)] ORACLE_HOME: $ORACLE_HOME" | tee -a "$LOGFILE"
echo "[$(date)] ORACLE_SID: $ORACLE_SID" | tee -a "$LOGFILE"
echo "[$(date)] BACKUP_DIR: $BACKUP_DIR" | tee -a "$LOGFILE"
echo "[$(date)] ========================================" | tee -a "$LOGFILE"

# 检查磁盘空间
echo "[$(date)] Checking disk space..." | tee -a "$LOGFILE"
df -h "$BACKUP_DIR" >> "$LOGFILE"
AVAILABLE_SPACE=$(df -BG "$BACKUP_DIR" | awk 'NR==2 {print $4}' | tr -d 'G')
if [ "$AVAILABLE_SPACE" -lt 50 ]; then
  echo "[$(date)] ERROR: Insufficient disk space (<50GB available)" | tee -a "$LOGFILE"
  exit 1
fi

# 备份前数据库状态检查
echo "[$(date)] Checking database status before backup..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT 'DB_STATUS: ' || STATUS FROM V\$INSTANCE;
SELECT 'DB_MODE: ' || DATABASE_ROLE FROM V\$DATABASE;
SELECT 'ARCHIVE_MODE: ' || LOG_MODE FROM V\$DATABASE;
EXIT;
EOF

# 备份重要配置文件（在关闭数据库前）
echo "[$(date)] Backing up configuration files..." | tee -a "$LOGFILE"
if [ -f "$ORACLE_HOME/dbs/spfile$ORACLE_SID.ora" ]; then
  cp "$ORACLE_HOME/dbs/spfile$ORACLE_SID.ora" "$BACKUP_DIR/config/spfile$ORACLE_SID.ora.$BACKUP_DATE" 2>/dev/null
  echo "[$(date)] Backed up spfile" | tee -a "$LOGFILE"
fi
if [ -f "$ORACLE_HOME/dbs/init$ORACLE_SID.ora" ]; then
  cp "$ORACLE_HOME/dbs/init$ORACLE_SID.ora" "$BACKUP_DIR/config/init$ORACLE_SID.ora.$BACKUP_DATE" 2>/dev/null
  echo "[$(date)] Backed up init file" | tee -a "$LOGFILE"
fi
if [ -f "$ORACLE_HOME/dbs/orapw$ORACLE_SID" ]; then
  cp "$ORACLE_HOME/dbs/orapw$ORACLE_SID" "$BACKUP_DIR/config/orapw$ORACLE_SID.$BACKUP_DATE" 2>/dev/null
  echo "[$(date)] Backed up password file" | tee -a "$LOGFILE"
fi

# 备份tnsnames.ora和listener.ora
if [ -f "$ORACLE_HOME/network/admin/tnsnames.ora" ]; then
  cp "$ORACLE_HOME/network/admin/tnsnames.ora" "$BACKUP_DIR/config/tnsnames.ora.$BACKUP_DATE" 2>/dev/null
  echo "[$(date)] Backed up tnsnames.ora" | tee -a "$LOGFILE"
fi
if [ -f "$ORACLE_HOME/network/admin/listener.ora" ]; then
  cp "$ORACLE_HOME/network/admin/listener.ora" "$BACKUP_DIR/config/listener.ora.$BACKUP_DATE" 2>/dev/null
  echo "[$(date)] Backed up listener.ora" | tee -a "$LOGFILE"
fi

# 启动数据库到 MOUNT 状态（关闭的数据库执行备份）
echo "[$(date)] Shutting down database and starting in MOUNT mode..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SHUTDOWN IMMEDIATE;
STARTUP MOUNT;
EXIT;
EOF

if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Failed to start database in MOUNT mode" | tee -a "$LOGFILE"
  exit 1
fi

# 执行RMAN冷备份（与还原脚本兼容的格式）
echo "[$(date)] Starting RMAN cold backup..." | tee -a "$LOGFILE"
rman target / log="$LOGFILE" <<EOF
RUN {
  # 配置RMAN参数
  CONFIGURE RETENTION POLICY TO REDUNDANCY 2;
  CONFIGURE BACKUP OPTIMIZATION ON;
  CONFIGURE DEFAULT DEVICE TYPE TO DISK;
  CONFIGURE CONTROLFILE AUTOBACKUP ON;

  # 备份数据库（直接放在备份目录根下，便于还原脚本查找）
  BACKUP AS COMPRESSED BACKUPSET DATABASE
    FORMAT '$BACKUP_DIR/coldbkp_%d_%T_%U.bkp'
    TAG='COLD_DB_BACKUP_$BACKUP_DATE'
    PLUS ARCHIVELOG
    FORMAT '$BACKUP_DIR/arch_%d_%T_%U.arc'
    TAG='COLD_ARCH_BACKUP_$BACKUP_DATE';

  # 备份当前控制文件（使用还原脚本期望的格式）
  BACKUP CURRENT CONTROLFILE
    FORMAT '$BACKUP_DIR/ctlfile_%d_%T.ctl'
    TAG='COLD_CTL_BACKUP_$BACKUP_DATE';

  # 备份SPFILE
  BACKUP SPFILE
    FORMAT '$BACKUP_DIR/spfile_%d_%T_%U.bkp'
    TAG='COLD_SPFILE_BACKUP_$BACKUP_DATE';

  # 创建控制文件的文本备份
  SQL "ALTER DATABASE BACKUP CONTROLFILE TO TRACE AS ''$BACKUP_DIR/config/create_controlfile_$BACKUP_DATE.sql''";

  # 验证备份
  RESTORE DATABASE VALIDATE;

  # 列出备份信息
  LIST BACKUP SUMMARY;

  # 清理过期备份
  DELETE NOPROMPT OBSOLETE;

  # 交叉检查备份
  CROSSCHECK BACKUP;
}
EXIT;
EOF

if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: RMAN backup failed" | tee -a "$LOGFILE"
  exit 1
fi

# 生成数据库结构信息
echo "[$(date)] Generating database structure information..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$BACKUP_DIR/config/db_structure_$BACKUP_DATE.txt" 2>&1
SET PAGESIZE 1000 LINESIZE 200
SPOOL $BACKUP_DIR/config/db_info_$BACKUP_DATE.txt
SELECT 'DATABASE INFO:' AS INFO_TYPE FROM DUAL;
SELECT NAME, DBID, CREATED, LOG_MODE FROM V\$DATABASE;
SELECT 'DATAFILES:' AS INFO_TYPE FROM DUAL;
SELECT FILE_ID, FILE_NAME, TABLESPACE_NAME, BYTES/1024/1024 AS SIZE_MB FROM DBA_DATA_FILES ORDER BY FILE_ID;
SELECT 'TEMPFILES:' AS INFO_TYPE FROM DUAL;
SELECT FILE_ID, FILE_NAME, TABLESPACE_NAME, BYTES/1024/1024 AS SIZE_MB FROM DBA_TEMP_FILES ORDER BY FILE_ID;
SELECT 'CONTROLFILES:' AS INFO_TYPE FROM DUAL;
SELECT NAME FROM V\$CONTROLFILE;
SELECT 'LOGFILES:' AS INFO_TYPE FROM DUAL;
SELECT GROUP#, MEMBER FROM V\$LOGFILE ORDER BY GROUP#;
SELECT 'TABLESPACES:' AS INFO_TYPE FROM DUAL;
SELECT TABLESPACE_NAME, STATUS, CONTENTS FROM DBA_TABLESPACES;
SPOOL OFF
EXIT;
EOF

# 备份重要的Oracle目录结构信息
echo "[$(date)] Backing up directory structure..." | tee -a "$LOGFILE"
find /opt/oracle/oradata -type f -name "*.dbf" -o -name "*.ctl" -o -name "*.log" > "$BACKUP_DIR/config/oracle_files_$BACKUP_DATE.txt" 2>/dev/null
ls -la /opt/oracle/oradata/ > "$BACKUP_DIR/config/oradata_structure_$BACKUP_DATE.txt" 2>/dev/null

# 创建备份清单
echo "[$(date)] Creating backup inventory..." | tee -a "$LOGFILE"
cat > "$BACKUP_DIR/backup_inventory_$BACKUP_DATE.txt" <<EOF
Oracle Database Cold Backup Inventory
=====================================
Backup Date: $BACKUP_DATE
Oracle SID: $ORACLE_SID
Oracle Home: $ORACLE_HOME

Backup Contents:
- Database datafiles (compressed)
- Control files (binary and trace)
- Archive logs
- SPFILE/PFILE
- Password file
- Network configuration files
- Database structure information

Backup Files:
EOF

find "$BACKUP_DIR" -name "*$BACKUP_DATE*" -type f -exec ls -lh {} \; >> "$BACKUP_DIR/backup_inventory_$BACKUP_DATE.txt"
find "$BACKUP_DIR" -name "*$(date +%Y%m%d)*" -type f -exec ls -lh {} \; >> "$BACKUP_DIR/backup_inventory_$BACKUP_DATE.txt"

# 计算备份大小
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
echo "Total Backup Size: $BACKUP_SIZE" >> "$BACKUP_DIR/backup_inventory_$BACKUP_DATE.txt"

# 备份完成后关闭数据库
echo "[$(date)] Shutting down database..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SHUTDOWN IMMEDIATE;
EXIT;
EOF

# 备份完成总结
echo "[$(date)] ========================================" | tee -a "$LOGFILE"
echo "[$(date)] === ORACLE COMPLETE COLD BACKUP END ===" | tee -a "$LOGFILE"
echo "[$(date)] Backup Location: $BACKUP_DIR" | tee -a "$LOGFILE"
echo "[$(date)] Total Backup Size: $BACKUP_SIZE" | tee -a "$LOGFILE"
echo "[$(date)] Backup Inventory: $BACKUP_DIR/backup_inventory_$BACKUP_DATE.txt" | tee -a "$LOGFILE"
echo "[$(date)] Log File: $LOGFILE" | tee -a "$LOGFILE"
echo "[$(date)] ========================================" | tee -a "$LOGFILE"

# 发送备份完成通知（可选）
echo "Oracle Database $ORACLE_SID cold backup completed successfully at $(date)" | tee -a "$LOGFILE"
echo "Backup size: $BACKUP_SIZE" | tee -a "$LOGFILE"
